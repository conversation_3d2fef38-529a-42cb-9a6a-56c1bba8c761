services:
  website2025:
    image: publiweb/php82-nginx-dev:laravel
    restart: always
    ports:
      - "8000:80"
    environment:
      # Configuración de la aplicación
      APP_NAME: 'Multibolsa Inmobiliaria'
      APP_ENV: development
      APP_DOMAIN: 'website2025.cft.mulbin.com'
      APP_URL: 'https://website2025.cft.mulbin.com'
      MULBIN_DOMAIN: 'cft.mulbin.com'
      APP_KEY: 'base64:Yww4gHEHQq9bUdOLPqswPxqXrD+wdZ6RzxfcV0FTdU0='
      APP_DEBUG: true
      APP_LOCALE: es
      APP_FAKER_LOCALE: es_MX
      FORCE_HTTPS: true
      FORCE_LIGHT_MODE: true
      # Configuración de base de datos
      PW_DB_CONNECTION: publiweb
      PW_DB_HOST: mariadb
      PW_DB_PORT: 3306
      PW_DB_DATABASE: publiweb
      PW_DB_USERNAME: root
      PW_DB_PASSWORD: password
      SI_DB_CONNECTION: sistemainmobiliario
      SI_DB_HOST: mariadb
      SI_DB_PORT: 3306
      SI_DB_DATABASE: sistemainmobiliario
      SI_DB_USERNAME: root
      SI_DB_PASSWORD: password
      DB_CONNECTION: publiweb
      DB_HOST: mariadb
      DB_PORT: 3306
      DB_DATABASE: publiweb
      DB_USERNAME: root
      DB_PASSWORD: password
      # Configuración de correo
      MAIL_MAILER: smtp
      MAIL_SCHEME: smtp
      MAIL_HOST: smtp-relay.brevo.com
      MAIL_PORT: 587
      MAIL_USERNAME: '<EMAIL>'
      MAIL_PASSWORD: 'xsmtpsib-56281fc4d62a4543971d1c4eab4905517995abf39d8da4056232ac8fee0dbc3f-PCj8TARmwFrW17dD'
      MAIL_ENCRYPTION: tls
      MAIL_FROM_NAME: 'NoReply (Mulbin)'
      MAIL_FROM_ADDRESS: '<EMAIL>'
      # Configuración de reCAPTCHA
      RECAPTCHA_SITE_KEY: '6Lc1sR4rAAAAAKTeVT-VZkCkhglT1n6kpJ4oJsiw'
      RECAPTCHA_SECRET_KEY: '6Lc1sR4rAAAAAFcZFQ7WksS5CPll-U-KfOzGLEmz'
      # Configuración de Conekta
      CONEKTA_PUBLIC_KEY: 'key_HK8PyepcfRNxp5WdrzfsWwQ'
      CONEKTA_PRIVATE_KEY: 'key_neq5cxH5UREuP8Tcg0VB7lZ'
      # Configuración de Google OAuth
      GOOGLE_CLIENT_ID: '491454724906-6f8fkpoqpboagtqd7futu91r1vclr42v.apps.googleusercontent.com'
      GOOGLE_CLIENT_SECRET: '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'
      GOOGLE_REDIRECT_URI: 'https://website2025.cft.mulbin.com/auth/google/callback'
      # Configuración de Google Maps API Key
      GOOGLE_MAPS_API_KEY: 'AIzaSyDHJ6wYwZCjQx5QaZRIZ_cjYD9NsLGcNX0'
      # Configuración de Facebook OAuth
      FACEBOOK_CLIENT_ID: '650491671146325'
      FACEBOOK_CLIENT_SECRET: '********************************'
      FACEBOOK_REDIRECT_URI: 'https://website2025.cft.mulbin.com/auth/facebook/callback'
    volumes:
      - /Users/<USER>/git.mulb.in/si/website2025:/var/www/html
      - /Users/<USER>/git.mulb.in/si/website2025/docker/nginx/default.conf:/etc/nginx/http.d/default.conf
    depends_on:
      - mariadb

  mariadb:
    image: mariadb:10.6.15
    restart: always
    environment:
      MARIADB_ROOT_PASSWORD: password
      MARIADB_ALLOW_EMPTY_ROOT_PASSWORD: "no"
    ports:
      - "3306:3306"
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./database/setup-local-databases.sql:/docker-entrypoint-initdb.d/setup-local-databases.sql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    restart: always
    depends_on:
      - mariadb
    environment:
      PMA_HOST: mariadb
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: password
      UPLOAD_LIMIT: 50M
    ports:
      - "8081:80"

  interno:
    image: publiweb/nginx-phpfpm:alpine-p5.6.40-base
    restart: always
    ports:
      - "8080:80"
    volumes:
      - '/Users/<USER>/git.mulb.in/Publiweb/interno:/var/www/html'
      - '/Users/<USER>/git.mulb.in/Publiweb/interno/clases2:/var/clases'
    environment:
      SERVER_DB: mariadb
      DOMAIN_SI: 'sistemainmobiliario.com'
      DOMAIN_PW: 'publiweb.mx'
      DEV: 'charlie'
      DB_SI: 'sistemainmobiliario'
      DB_PW: 'publiweb'
      MYSQL_USER: 'root'
      MYSQL_PASS: 'password'
    depends_on:
      - mariadb

  msi-v5:
    image: publiweb/phpfpm-alpine:8.2-0.0.nginx-DEV
    restart: always
    environment:
      APP_ENV: development
      APP_DOMAIN: 'https://website2025.cft.mulbin.com'
      SI_MYSQL_SERVER: mariadb
      SI_MYSQL_USERNAME: root
      SI_MYSQL_PASSWORD: password
      SI_MYSQL_DATABASE: sistemainmobiliario
      PW_MYSQL_SERVER: mariadb
      PW_MYSQL_DATABASE: publiweb
      PW_MYSQL_USERNAME: root
      PW_MYSQL_PASSWORD: password
      BREVO_API_KEY: '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'
      BREVO_API_URL: 'https://api.brevo.com/v3'
      BREVO_SMTP_SERVER: 'smtp-relay.brevo.com'
      BREVO_SMTP_PORT: '587'
      BREVO_SMTP_USERNAME: '<EMAIL>'
      BREVO_FROM_EMAIL: '<EMAIL>'
      BREVO_SMTP_PASSWORD: 'xsmtpsib-56281fc4d62a4543971d1c4eab4905517995abf39d8da4056232ac8fee0dbc3f-PCj8TARmwFrW17dD'
    ports:
      - "8082:80"
    volumes:
      - /Users/<USER>/git.mulb.in/si/v5/msi-v5:/var/www/html
      - /Users/<USER>/git.mulb.in/si/v5/msi-v5/server/nginx:/etc/nginx/http.d
      - /Users/<USER>/git.mulb.in/si/v5/components-vue-v5.vite/dist:/var/www/html/public/dist

  panel4:
    image: publiweb/nginx-phpfpm5:20250510-dev
    ports:
      - "8020:80"
    volumes:
      - 'php_session:/var/lib/php/session'
      - '/Users/<USER>/git.mulb.in/si/v4/_panel4/containers/panel4:/var/www/html'
      - '/Users/<USER>/git.mulb.in/si/v4/_panel4/containers/panel4-templates:/var/www/templates'
      - '/Users/<USER>/git.mulb.in/si/v4/_panel4/si_v4.subs/themes:/var/www/themes'
      - '/Users/<USER>/git.mulb.in/si/v4/_panel4/composer:/home/<USER>'
      - '/Users/<USER>/git.mulb.in/si/v4/_panel4/containers/panel4-nginx-php5-docker-image/default.nginx.conf:/etc/nginx/conf.d/default.conf'
      - '/Users/<USER>/git.mulb.in/si/v4/_panel4/containers/panel4-nginx-php5-docker-image/auth-session:/var/www/auth-session'
    restart: always
    environment:
      NGINX_HOST: 'localhost'
      PHP_MEMORY_LIMIT: '256M'
      DB_SERVER: 'mariadb'
      DB_USER: 'root'
      DB_PASSWORD: 'password'
      DB_NAME: 'sistemainmobiliario'
      DB_SI: 'sistemainmobiliario'
      DB_PW: 'publiweb'
      ENV: 'development'
      MULBIN_NAME: 'Mulbin'
      MULBIN_PHONE: '527774580651'
      MULBIN_EMAIL: '<EMAIL>'
      MULBIN_URL: 'https://mulbin.com'
      MASTER_WEB_DOMAIN: '.web.cft.mulbin.com'
      MASTER_PANEL_DOMAIN: '.panel.cft.mulbin.com'
    extra_hosts:
      - "pics.server:************"

volumes:
  mariadb_data:
    driver: local
  php_session:
    driver: local

networks:
  default:
    driver: bridge