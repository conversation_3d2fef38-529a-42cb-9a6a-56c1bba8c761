<?php

use App\Http\Controllers\Auth\FacebookController;
use App\Http\Controllers\Auth\GoogleController;
use App\Http\Controllers\Auth\GuestVerifyEmailController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\VerifyEmailChangeController;
use App\Http\Controllers\Auth\VerifyEmailController;
use App\Livewire\Actions\Logout;
use App\Livewire\Auth\Login;
use App\Livewire\Auth\Register;
use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;

Route::middleware('guest')->group(function () {
    Route::get('login', Login::class)
        ->name('login');

//    Route::get('register', Register::class)
//        ->name('register');

    Route::group(['prefix' => 'register'], function () {
        Route::get('/', [RegisterController::class, 'showRegistrationForm'])
            ->name('register');
        Route::get('/{token}', [RegisterController::class, 'showRegistrationFormWithToken'])
            ->name('register.with.token');
    });

    Volt::route('forgot-password', 'auth.forgot-password')
        ->name('password.request');

    Volt::route('reset-password/{token}', 'auth.reset-password')
        ->name('password.reset');

    // Rutas para autenticación con Google
    Route::get('auth/google', [GoogleController::class, 'redirectToGoogle'])
        ->name('auth.google');
    Route::get('auth/google/callback', [GoogleController::class, 'handleGoogleCallback']);

    // Rutas para autenticación con Facebook
    Route::get('auth/facebook', [FacebookController::class, 'redirectToFacebook'])
        ->name('auth.facebook');
    Route::get('auth/facebook/callback', [FacebookController::class, 'handleFacebookCallback']);
});

Route::middleware('auth')->group(function () {
    Volt::route('verify-email', 'auth.verify-email')
        ->name('verification.notice');

    Route::get('verify-email/{id}/{hash}', VerifyEmailController::class)
        ->middleware(['signed', 'throttle:6,1'])
        ->name('verification.verify');

    Volt::route('confirm-password', 'auth.confirm-password')
        ->name('password.confirm');
});

// Ruta para verificar el email sin autenticación
Route::get('guest-verify-email/{id}/{hash}', GuestVerifyEmailController::class)
    ->middleware(['signed', 'throttle:6,1'])
    ->name('verification.verify.guest');

// Ruta para verificar el cambio de correo electrónico
Route::get('verify-email-change/{id}/{email}/{hash}', VerifyEmailChangeController::class)
    ->middleware(['signed', 'throttle:6,1'])
    ->name('verification.verify-email-change');

Route::post('logout', Logout::class)
    ->name('logout');
