#!/bin/sh

# Definir variables
IMAGE_NAME="php82-nginx-dev"
REPOSITORY="publiweb"
TAG="laravel"

echo "Construyendo la imagen multiplataforma con la configuración actualizada..."
docker buildx build \
    --file Dockerfile.developer \
    --platform linux/amd64,linux/arm64/v8 \
    -t $REPOSITORY/$IMAGE_NAME:$TAG \
    --push .

echo "Proceso completado."
echo "La imagen ha sido actualizada en $REPOSITORY/$IMAGE_NAME:$TAG"
