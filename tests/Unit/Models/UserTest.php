<?php

namespace Tests\Unit\Models;

use App\Models\User;
use PHPUnit\Framework\TestCase;

class UserTest extends TestCase
{
    public function test_pabusqueda_is_updated_when_creating_user_object()
    {
        // Crear un objeto User sin guardarlo en la base de datos
        $user = new User([
            'usuario' => 'usuarioprueba',
            'logmail' => '<EMAIL>',
            'nombre' => 'Nombre',
            'apellidos' => 'Apellidos',
            'empresa' => 'Empresa Ejemplo',
            'calle_numero' => 'Calle 123',
            'colonia' => 'Colonia',
            'codigo_postal' => 12345,
            'ciudad' => 'Ciudad',
            'estado' => 'Estado',
            'pais' => 'México',
            'telefono' => '1234567890',
            'celular' => '0987654321',
            'email' => '<EMAIL>',
            'email_sec' => '<EMAIL>',
            'sitio_web' => 'www.ejemplo.com',
            'fact_nombre' => 'Factura Nombre',
            'fact_domicilio' => 'Domicilio Fiscal',
            'fact_rfc' => 'RFC123456789',
        ]);

        // Llamar manualmente al método updatePaBusqueda
        $reflectionClass = new \ReflectionClass(User::class);
        $method = $reflectionClass->getMethod('updatePaBusqueda');
        $method->setAccessible(true);
        $method->invoke($user);

        // Verificar que el campo pabusqueda contiene todos los campos especificados
        $pabusqueda = $user->pabusqueda;

        $this->assertStringContainsString('usuarioprueba', $pabusqueda);
        $this->assertStringContainsString('<EMAIL>', $pabusqueda);
        $this->assertStringContainsString('Nombre', $pabusqueda);
        $this->assertStringContainsString('Apellidos', $pabusqueda);
        $this->assertStringContainsString('Empresa Ejemplo', $pabusqueda);
        $this->assertStringContainsString('Calle 123', $pabusqueda);
        $this->assertStringContainsString('Colonia', $pabusqueda);
        $this->assertStringContainsString('12345', $pabusqueda);
        $this->assertStringContainsString('Ciudad', $pabusqueda);
        $this->assertStringContainsString('Estado', $pabusqueda);
        $this->assertStringContainsString('México', $pabusqueda);
        $this->assertStringContainsString('1234567890', $pabusqueda);
        $this->assertStringContainsString('0987654321', $pabusqueda);
        $this->assertStringContainsString('<EMAIL>', $pabusqueda);
        $this->assertStringContainsString('www.ejemplo.com', $pabusqueda);
        $this->assertStringContainsString('Factura Nombre', $pabusqueda);
        $this->assertStringContainsString('Domicilio Fiscal', $pabusqueda);
        $this->assertStringContainsString('RFC123456789', $pabusqueda);
    }

    public function test_pabusqueda_is_updated_when_updating_user_object()
    {
        // Crear un objeto User sin guardarlo en la base de datos
        $user = new User([
            'nombre' => 'Nombre Original',
            'apellidos' => 'Apellidos Original',
            'email' => '<EMAIL>',
            'logmail' => '<EMAIL>',
        ]);

        // Llamar manualmente al método updatePaBusqueda
        $reflectionClass = new \ReflectionClass(User::class);
        $method = $reflectionClass->getMethod('updatePaBusqueda');
        $method->setAccessible(true);
        $method->invoke($user);

        // Verificar que el campo pabusqueda contiene solo los datos iniciales
        $this->assertStringContainsString('Nombre Original', $user->pabusqueda);
        $this->assertStringContainsString('Apellidos Original', $user->pabusqueda);
        $this->assertStringContainsString('<EMAIL>', $user->pabusqueda);

        // Actualizar el usuario con más información
        $user->empresa = 'Nueva Empresa';
        $user->ciudad = 'Nueva Ciudad';
        $user->telefono = '5566778899';

        // Llamar manualmente al método updatePaBusqueda de nuevo
        $method->invoke($user);

        // Verificar que el campo pabusqueda se actualizó con los nuevos datos
        $this->assertStringContainsString('Nombre Original', $user->pabusqueda);
        $this->assertStringContainsString('Apellidos Original', $user->pabusqueda);
        $this->assertStringContainsString('<EMAIL>', $user->pabusqueda);
        $this->assertStringContainsString('Nueva Empresa', $user->pabusqueda);
        $this->assertStringContainsString('Nueva Ciudad', $user->pabusqueda);
        $this->assertStringContainsString('5566778899', $user->pabusqueda);
    }

    public function test_website_url_generation_without_custom_domain()
    {
        // Crear un objeto User sin guardarlo en la base de datos
        $user = new User([
            'usuario' => 'testuser',
        ]);

        // Mock de la relación siConfig que retorna null (sin dominio personalizado)
        $user->setRelation('siConfig', null);

        $expectedUrl = 'https://testuser.web.mulbin.com';
        $this->assertEquals($expectedUrl, $user->getWebsiteUrl());
    }

    public function test_panel_url_generation_without_custom_domain()
    {
        // Crear un objeto User sin guardarlo en la base de datos
        $user = new User([
            'usuario' => 'testuser',
        ]);

        // Mock de la relación siConfig que retorna null (sin dominio personalizado)
        $user->setRelation('siConfig', null);

        $expectedUrl = 'https://testuser.panel.mulbin.com';
        $this->assertEquals($expectedUrl, $user->getPanelUrl());
    }

    public function test_website_url_generation_with_custom_domain()
    {
        // Crear un objeto User sin guardarlo en la base de datos
        $user = new User([
            'usuario' => 'testuser',
        ]);

        // Mock de SIConfig con dominio personalizado
        $siConfig = new \App\Models\SIConfig([
            'dominio' => 'ejemplo.com',
        ]);
        $user->setRelation('siConfig', $siConfig);

        $expectedUrl = 'https://ejemplo.com';
        $this->assertEquals($expectedUrl, $user->getWebsiteUrl());
    }

    public function test_panel_url_generation_with_custom_domain()
    {
        // Crear un objeto User sin guardarlo en la base de datos
        $user = new User([
            'usuario' => 'testuser',
        ]);

        // Mock de SIConfig con dominio personalizado
        $siConfig = new \App\Models\SIConfig([
            'dominio' => 'ejemplo.com',
        ]);
        $user->setRelation('siConfig', $siConfig);

        $expectedUrl = 'https://panel.ejemplo.com';
        $this->assertEquals($expectedUrl, $user->getPanelUrl());
    }

    public function test_url_generation_with_empty_usuario()
    {
        // Crear un objeto User sin usuario
        $user = new User([
            'usuario' => '',
        ]);

        $this->assertEquals('', $user->getWebsiteUrl());
        $this->assertEquals('', $user->getPanelUrl());
    }
}