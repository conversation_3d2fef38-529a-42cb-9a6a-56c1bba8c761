<?php

namespace Tests\Feature\Livewire;

use App\Livewire\DashboardAndWebsiteAccess;
use App\Models\SIConfig;
use App\Models\User;
use Livewire\Livewire;
use Tests\TestCase;

class DashboardAndWebsiteAccessTest extends TestCase
{
    public function test_component_renders_correctly_without_custom_domain()
    {
        // Crear un usuario de prueba
        $user = new User([
            'usuario' => 'testuser',
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Mock de la relación siConfig que retorna null (sin dominio personalizado)
        $user->setRelation('siConfig', null);

        // Simular autenticación
        $this->actingAs($user);

        // Renderizar el componente
        $component = Livewire::test(DashboardAndWebsiteAccess::class);

        // Verificar que las URLs se generan correctamente
        $component->assertSet('websiteUrl', 'https://testuser.web.mulbin.com');
        $component->assertSet('dashboardUrl', 'https://testuser.panel.mulbin.com');

        // Verificar que la vista se renderiza
        $component->assertStatus(200);
    }

    public function test_component_renders_correctly_with_custom_domain()
    {
        // Crear un usuario de prueba
        $user = new User([
            'usuario' => 'testuser',
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Mock de SIConfig con dominio personalizado
        $siConfig = new SIConfig([
            'dominio' => 'ejemplo.com',
        ]);
        $user->setRelation('siConfig', $siConfig);

        // Simular autenticación
        $this->actingAs($user);

        // Renderizar el componente
        $component = Livewire::test(DashboardAndWebsiteAccess::class);

        // Verificar que las URLs se generan correctamente con dominio personalizado
        $component->assertSet('websiteUrl', 'https://ejemplo.com');
        $component->assertSet('dashboardUrl', 'https://panel.ejemplo.com');

        // Verificar que la vista se renderiza
        $component->assertStatus(200);
    }

    public function test_component_handles_empty_usuario()
    {
        // Crear un usuario de prueba sin usuario
        $user = new User([
            'usuario' => '',
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Mock de la relación siConfig
        $user->setRelation('siConfig', null);

        // Simular autenticación
        $this->actingAs($user);

        // Renderizar el componente
        $component = Livewire::test(DashboardAndWebsiteAccess::class);

        // Verificar que las URLs están vacías cuando no hay usuario
        $component->assertSet('websiteUrl', '');
        $component->assertSet('dashboardUrl', '');

        // Verificar que la vista se renderiza
        $component->assertStatus(200);
    }
}
