<div>
    <!-- Conekta script is now loaded directly in the conekta-card-form component via Alpine's x-init -->

    <div class="space-y-6">
        <!-- Paso 1: Selección de plan -->
        @if ($currentStep == 1)
            @livewire('plan-selector')
        @endif

        <!-- Paso 2: Formulario de datos de contacto -->
        @if ($currentStep == 2)
            <form wire:submit.prevent="submit" class="space-y-6" id="datos-formulario">
                <!-- Resumen del plan seleccionado -->
                <div class="bg-indigo-50 p-6 rounded-lg border border-indigo-100 mb-8 max-w-2xl mx-auto">
                    <h3 class="text-lg font-medium text-gray-900 mb-4 text-center">Plan seleccionado</h3>
                    <div class="flex flex-col md:flex-row justify-between items-center">
                        <div class="mb-4 md:mb-0 text-center md:text-left">
                            <p class="text-base font-medium text-gray-900">
                                @if ($selectedPlan == 'SI-FREE')
                                    Plan Freemium
                                    @if ($addMsComplement)
                                        + {{ $msComplementName }}
                                    @endif
                                @elseif ($selectedPlan == 'SI-WEB')
                                    Plan WEB
                                @elseif ($selectedPlan == 'SI-PRO')
                                    Plan PRO
                                @endif
                                -
                                @if ($billingPeriod == 'monthly')
                                    Mensual
                                @elseif ($billingPeriod == 'quarterly')
                                    Trimestral
                                @elseif ($billingPeriod == 'biannual')
                                    Semestral
                                @elseif ($billingPeriod == 'annual')
                                    Anual
                                @endif
                            </p>
                            <p class="text-sm text-gray-600 mt-1">
                                Precio mensual: ${{ number_format($planPrice, 0) }} MXN
                            </p>
                        </div>
                        <div class="text-center md:text-right">
                            <p class="text-xl font-bold text-gray-900">${{ number_format($totalPrice, 0) }} MXN</p>
                            <button
                                    type="button"
                                    wire:click="previousStep"
                                    class="text-sm text-indigo-600 hover:text-indigo-800 font-medium mt-1"
                            >
                                Cambiar plan
                            </button>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <p class="text-lg text-gray-800 font-medium text-center @if (empty($username) || !$usernameAvailable) opacity-30 bg-gray-300 @endif">
                        Su portal web se activará en la
                        dirección:</p>

                    <!-- Vista previa del portal web -->
                    <div class="text-center @if (empty($username) || !$usernameAvailable) opacity-30 bg-gray-300 @endif">
                        <p class="text-xl text-indigo-600">
                            https://<strong>{{ $username }}</strong>.mulbin.com
                        </p>
                        <p class="text-sm text-gray-600 mt-2">
                            Posteriormente, podrá configurar su propio dominio personalizado.
                        </p>
                    </div>

                    <!-- Campo de nombre de usuario editable -->
                    <div class="mt-4">
                        <div class="relative">
                            <label for="username" class="block text-sm font-medium leading-6 text-gray-900">
                                {{ __('Nombre de la agencia') }}
                            </label>
                            <div class="mt-2">
                                <input
                                        type="text"
                                        id="username"
                                        autocomplete="off"
                                        wire:model.blur="username"
                                        placeholder="{{ !empty($username) ? $username : 'nombre-usuario' }}"
                                        class="block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 @error('username') ring-red-500 @enderror"
                                        wire:target="performUsernameCheck"
                                        wire:loading.class="opacity-50"
                                >
                            </div>

                            <script>
                                document.addEventListener('livewire:initialized', () => {
                                    // Escuchar el evento username-processed
                                    @this.
                                    on('username-processed', ({username}) => {
                                        // Actualizar el valor del input
                                        document.getElementById('username').value = username;
                                    });

                                    // Agregar evento para capturar el valor original antes de enviarlo
                                    document.getElementById('username').addEventListener('change', function (e) {
                                        const originalValue = e.target.value;
                                        // Guardar el valor original para enviarlo al componente
                                        @this.
                                        set('empresa', originalValue);
                                    });
                                });
                            </script>

                            @error('username')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror

                            <!-- Indicador de disponibilidad -->
                            <div class="mt-2">
                                @if ($checkingUsername)
                                    <div class="flex items-center text-sm text-gray-500">
                                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-indigo-500"
                                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                    stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor"
                                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        <span>Verificando disponibilidad...</span>
                                    </div>
                                @elseif ($usernameAvailable === true)
                                    <div class="flex items-center text-sm text-blue-600">
                                        <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                             fill="currentColor">
                                            <path fill-rule="evenodd"
                                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                        <span>Nombre de usuario disponible</span>
                                    </div>
                                @elseif ($usernameAvailable === false)
                                    <div class="flex items-center text-sm text-red-600">
                                        <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                             fill="currentColor">
                                            <path fill-rule="evenodd"
                                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                        <span>Nombre de usuario no disponible</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Campo de teléfono -->
                    <div class="mt-6">
                        <label for="phone_combo" class="block text-sm font-medium leading-6 text-gray-900">
                            {{ __('Número de teléfono') }}
                        </label>
                        <div class="relative mt-2 rounded-md">
                            <div class="grid grid-cols-5 gap-2">
                                <div class="col-span-2 relative">
                                    <select wire:model.change="phone_country_code" id="phone_country_code"
                                            class="block w-full rounded-md border-0 py-2.5 pl-3 pr-8 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 appearance-none @error('phone_country_code') ring-red-500 border-red-500 @enderror">
                                        <option value="">{{ __('Seleccionar') }}</option>
                                        <option value="+52">+52 (México)</option>
                                        <option value="+1">+1 (USA/Canadá)</option>
                                        <option value="+34">+34 (España)</option>
                                        <option value="+57">+57 (Colombia)</option>
                                        <option value="+56">+56 (Chile)</option>
                                        <option value="+51">+51 (Perú)</option>
                                        <option value="+54">+54 (Argentina)</option>
                                        <option value="+593">+593 (Ecuador)</option>
                                        <option value="+502">+502 (Guatemala)</option>
                                        <option value="+503">+503 (El Salvador)</option>
                                        <option value="+504">+504 (Honduras)</option>
                                        <option value="+505">+505 (Nicaragua)</option>
                                        <option value="+506">+506 (Costa Rica)</option>
                                        <option value="+507">+507 (Panamá)</option>
                                        <option value="+58">+58 (Venezuela)</option>
                                        <option value="+591">+591 (Bolivia)</option>
                                        <option value="+595">+595 (Paraguay)</option>
                                        <option value="+598">+598 (Uruguay)</option>
                                        <option value="+55">+55 (Brasil)</option>
                                        <option value="+351">+351 (Portugal)</option>
                                    </select>
                                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                                        <svg class="h-4 w-4 fill-current" xmlns="http://www.w3.org/2000/svg"
                                             viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    @error('phone_country_code')
                                    <p class="mt-1 text-sm text-red-600">{{ __('Por favor selecciona un código de país') }}</p>
                                    @enderror
                                </div>
                                <div class="col-span-3">
                                    <input type="tel" wire:model.blur="phone_number" id="phone_number"
                                           class="block w-full rounded-md border-0 py-2.5 pl-3 pr-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 @error('phone_number') ring-red-500 @enderror"
                                           placeholder="************" inputmode="numeric"
                                           x-mask="************"
                                           maxlength="12">
                                </div>
                            </div>
                            <p class="mt-1 text-xs text-gray-500">
                                {{ __('Selecciona el código de país e ingresa tu número sin el código.') }}
                            </p>
                            @error('phone_number')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Campos de dirección con Google Places Autocomplete -->
                    <div class="mt-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Dirección</h4>

                        <!-- Campo de búsqueda con Autocomplete -->
                        <div class="mb-4">
                            <label for="autocomplete" class="block text-sm font-medium leading-6 text-gray-900">
                                {{ __('Busca tu domicilio') }}
                            </label>
                            <div class="mt-2">
                                <input type="text" id="autocomplete" autocomplete="off"
                                       class="block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                       placeholder="Empieza a escribir...">
                            </div>
                            <p class="mt-1 text-xs text-gray-500">
                                {{ __('Resultados restringidos a la República Mexicana.') }}
                            </p>
                        </div>

                        <!-- Calle y número -->
                        <div class="mb-4">
                            <label for="calle_numero" class="block text-sm font-medium leading-6 text-gray-900">
                                {{ __('Calle y número') }}
                            </label>
                            <div class="mt-2">
                                <input type="text" wire:model.blur="calle_numero" id="calle_numero"
                                       class="block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 @error('calle_numero') ring-red-500 @enderror"
                                       placeholder="Av. Juárez 123">
                            </div>
                            @error('calle_numero')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Colonia -->
                        <div class="mb-4">
                            <label for="colonia" class="block text-sm font-medium leading-6 text-gray-900">
                                {{ __('Colonia') }}
                            </label>
                            <div class="mt-2">
                                <input type="text" wire:model.blur="colonia" id="colonia"
                                       class="block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 @error('colonia') ring-red-500 @enderror"
                                       placeholder="Colonia o barrio">
                            </div>
                            @error('colonia')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Código postal, Ciudad y Estado en una fila -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div>
                                <label for="codigo_postal" class="block text-sm font-medium leading-6 text-gray-900">
                                    {{ __('Código postal') }}
                                </label>
                                <div class="mt-2">
                                    <input type="text" wire:model.blur="codigo_postal" id="codigo_postal"
                                           class="block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 @error('codigo_postal') ring-red-500 @enderror"
                                           x-mask="99999"
                                           placeholder="62290">
                                </div>
                                @error('codigo_postal')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="ciudad" class="block text-sm font-medium leading-6 text-gray-900">
                                    {{ __('Ciudad') }}
                                </label>
                                <div class="mt-2">
                                    <input type="text" wire:model.blur="ciudad" id="ciudad"
                                           class="block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 @error('ciudad') ring-red-500 @enderror"
                                           placeholder="Ciudad de México">
                                </div>
                                @error('ciudad')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="estado" class="block text-sm font-medium leading-6 text-gray-900">
                                    {{ __('Estado') }}
                                </label>
                                <div class="mt-2">
                                    <input type="text" wire:model.blur="estado" id="estado"
                                           class="block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 @error('estado') ring-red-500 @enderror"
                                           placeholder="CDMX">
                                </div>
                                @error('estado')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- País -->
                        <div class="mb-4">
                            <label for="pais" class="block text-sm font-medium leading-6 text-gray-900">
                                {{ __('País') }}
                            </label>
                            <div class="mt-2">
                                <input type="text"
                                       class="block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 @error('pais') ring-red-500 @enderror"
                                       value="{{ $pais }}" readonly>
                            </div>
                            <input type="hidden" id="pais" wire:model="pais">
                            @error('pais')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Formulario de pago personalizado para planes pagados o plan FREE con complemento SI-MS -->
                @if ($selectedPlan != 'SI-FREE' || ($selectedPlan == 'SI-FREE' && $addMsComplement))
                    <div class="border-t pt-6 mt-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Información de pago</h4>

                        @if (session('error'))
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                                {{ session('error') }}
                            </div>
                        @endif

                        @if ($errors->any())
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                                <ul class="list-disc pl-5">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <!-- Formulario de tarjeta con Conekta -->
                        @livewire('conekta-card-form', ['conektaTokenId' => $conektaTokenId], key('conekta-card-form'))
                    </div>
                @endif

                <div class="flex items-center justify-between">
                    <p class="text-xs text-gray-500">
                        Al enviar este formulario, acepta nuestros
                        <a href="{{ route('terminos.uso') }}" target="_blank"
                           class="font-medium text-indigo-600 hover:text-indigo-500">
                            términos de uso</a>.
                    </p>
                </div>

                <div class="mt-10 flex justify-center">
                    <button type="submit" id="submitButton"
                            class="w-full md:w-auto flex justify-center py-3 px-8 border border-transparent rounded-md shadow-lg text-base font-medium text-white {{ ($selectedPlan == 'SI-FREE' || $conektaTokenId) ? 'bg-green-600 hover:bg-green-700 animate-pulse' : 'bg-indigo-600 hover:bg-indigo-700' }} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:scale-105 max-w-md disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-indigo-600 disabled:hover:scale-100 disabled:animate-none cursor-pointer"
                            wire:loading.attr="disabled"
                            wire:loading.class="opacity-75 cursor-wait"
                            {{ (($selectedPlan != 'SI-FREE' || ($selectedPlan == 'SI-FREE' && $addMsComplement)) && !$conektaTokenId) ? 'disabled' : '' }}>
                        <span wire:loading.class="hidden">{{ (($selectedPlan == 'SI-FREE' && !$addMsComplement) || $conektaTokenId) ? '¡Solicitar activación ahora!' : 'Solicitar activación del portal web' }}</span>
                        <span wire:loading.class.remove="hidden" class="hidden flex items-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                 xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10"
                                        stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor"
                                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                </path>
                            </svg>
                            Procesando...
                        </span>
                    </button>
                </div>
            </form>
        @endif
    </div>
</div>

@push('scripts')
    @include('partials.scripts.google-places-autocomplete')
@endpush