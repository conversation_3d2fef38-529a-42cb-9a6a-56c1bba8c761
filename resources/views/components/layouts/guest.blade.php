<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $title ?? config('app.name', 'Multibolsa Inmobiliaria') }}</title>

    <!-- SEO Meta Tags -->
    <meta name="description"
          content="{{ $metaDescription ?? 'Sistema integral para profesionales inmobiliarios, conecta con otros colegas y expande tu red de negocios inmobiliarios.' }}"/>
    <meta name="keywords"
          content="{{ $metaKeywords ?? 'inmobiliaria, bienes raíces, portal inmobiliario, sistema inmobiliario, propiedades, casas, terrenos, venta, renta' }}"/>
    <meta name="author" content="Multibolsa Inmobiliaria"/>
    <meta name="robots" content="{{ $metaRobots ?? 'index, follow' }}"/>

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="{{ $ogType ?? 'website' }}"/>
    <meta property="og:url" content="{{ url()->current() }}"/>
    <meta property="og:title"
          content="{{ $title ?? config('app.name', 'Multibolsa Inmobiliaria') }}"/>
    <meta property="og:description"
          content="{{ $metaDescription ?? 'Sistema integral para profesionales inmobiliarios, conecta con otros colegas y expande tu red de negocios inmobiliarios.' }}"/>
    <meta property="og:image" content="{{ $ogImage ?? asset('images/og-mulbin-2.jpg') }}"/>
    <meta property="og:locale" content="es_MX"/>
    <meta property="og:site_name" content="{{ config('app.name') }}"/>

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image"/>
    <meta name="twitter:url" content="{{ url()->current() }}"/>
    <meta name="twitter:title"
          content="{{ $title ?? config('app.name', 'Multibolsa Inmobiliaria') }}"/>
    <meta name="twitter:description"
          content="{{ $metaDescription ?? 'Sistema integral para profesionales inmobiliarios, conecta con otros colegas y expande tu red de negocios inmobiliarios.' }}"/>
    <meta name="twitter:image" content="{{ $ogImage ?? asset('images/og-mulbin-2.jpg') }}"/>

    <!-- Canonical URL -->
    <link rel="canonical" href="{{ url()->current() }}"/>

    @yield('meta')

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ asset('images/mulbin-favicon.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('images/mulbin-apple-touch-icon.png') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet"/>

    <!-- Alpine.js Cloak Style -->
    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @fluxAppearance
</head>
<body class="font-sans antialiased text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-zinc-800 min-h-screen flex flex-col">
<header class="bg-white dark:bg-zinc-900 shadow" x-data="{ mobileMenuOpen: false }">
    <div class="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex">
                <div class="shrink-0 flex items-center">
                    <a href="{{ route('home') }}">
                        <x-application-logo variant="default" class="block h-9 w-auto dark:hidden"/>
                        <x-application-logo variant="white" class="hidden h-9 w-auto dark:block"/>
                    </a>
                </div>

                <div class="hidden space-x-8 md:-my-px md:ml-10 md:flex">
                    <a href="{{ route('home') }}"
                       class="{{ request()->routeIs('home') ? 'inline-flex items-center px-1 pt-1 border-b-2 border-indigo-500 text-sm font-medium leading-5 text-gray-900 dark:text-white focus:outline-none focus:border-indigo-700 transition' : 'inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium leading-5 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 focus:outline-none focus:text-gray-700 focus:border-gray-300 transition' }}">
                        Inicio
                    </a>
                    <a href="{{ route('home') }}#funcionalidades"
                       class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium leading-5 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 focus:outline-none focus:text-gray-700 focus:border-gray-300 transition">
                        Funcionalidades
                    </a>
                    <a href="{{ route('home') }}#precios"
                       class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium leading-5 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 focus:outline-none focus:text-gray-700 focus:border-gray-300 transition">
                        Precios
                    </a>
                    <a href="{{ route('home') }}#faq"
                       class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium leading-5 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 focus:outline-none focus:text-gray-700 focus:border-gray-300 transition">
                        FAQ
                    </a>
                    @auth
                        <a href="{{ route('dashboard') }}"
                           class="{{ request()->routeIs('dashboard') ? 'inline-flex items-center px-1 pt-1 border-b-2 border-indigo-500 text-sm font-medium leading-5 text-gray-900 dark:text-white focus:outline-none focus:border-indigo-700 transition' : 'inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium leading-5 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 focus:outline-none focus:text-gray-700 focus:border-gray-300 transition' }}">
                            <strong>Dashboard</strong>
                        </a>
                    @endauth
                </div>
            </div>

            <div class="flex items-center md:hidden">
                <!-- Mobile menu button -->
                <button @click="mobileMenuOpen = !mobileMenuOpen" type="button"
                        class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 dark:text-gray-200 hover:text-gray-500 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 cursor-pointer"
                        aria-controls="mobile-menu" aria-expanded="false">
                    <span class="sr-only">Abrir menú principal</span>
                    <!-- Icon when menu is closed -->
                    <svg x-bind:class="{'hidden': mobileMenuOpen, 'block': !mobileMenuOpen }" class="h-6 w-6"
                         xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"
                         aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M4 6h16M4 12h16M4 18h16"/>
                    </svg>
                    <!-- Icon when menu is open -->
                    <svg x-bind:class="{'block': mobileMenuOpen, 'hidden': !mobileMenuOpen }" class="h-6 w-6"
                         xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"
                         aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>

            <div class="hidden md:flex items-center">
                @auth
                    <!-- User dropdown -->
                    <div id="user-menu" class="ml-3 relative" x-data="{ open: false }">
                        <div>
                            <button @click="open = !open" type="button"
                                    class="max-w-xs flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer"
                                    id="user-menu-button" aria-expanded="false" aria-haspopup="true">
                                <span class="sr-only">Abrir menú de usuario</span>
                                @if(Auth::user()->avatar)
                                    <img src="{{ Auth::user()->avatar }}" alt="{{ Auth::user()->name }}"
                                         class="h-8 w-8 rounded-full">
                                @else
                                    <span class="inline-flex h-8 w-8 rounded-full bg-gray-500 dark:bg-gray-600 text-white items-center justify-center">
                                        {{ substr(Auth::user()->name, 0, 1) }}
                                    </span>
                                @endif
                            </button>
                        </div>
                        <div x-show="open" @click.away="open = false" x-cloak
                             class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white dark:bg-zinc-800 ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
                             role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button"
                             tabindex="-1">
                            @if(Auth::user()->isWebsiteActivated())
                                <a href="{{ route('dashboard') }}"
                                   class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-zinc-700"
                                   role="menuitem"><strong>Dashboard</strong></a>
                            @endif
                            <a href="{{ route('activar') }}"
                               class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-zinc-700"
                               role="menuitem">
                                @if(Auth::user()->isWebsiteActivated())
                                    Mi Plan
                                @else
                                    Activar Sistema
                                @endif
                            </a>
                            <a href="{{ route('settings.profile') }}"
                               class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-zinc-700"
                               role="menuitem">Configuración</a>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit"
                                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-zinc-700"
                                        role="menuitem">Cerrar Sesión
                                </button>
                            </form>
                        </div>
                    </div>
                @else
                    <a href="{{ route('login') }}"
                       class="text-sm text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 mr-4">Iniciar
                        sesión</a>
                    @if (Route::has('register'))
                        <a href="{{ route('register') }}"
                           class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">Registrarse</a>
                    @endif
                @endauth
            </div>
        </div>
    </div>

    <!-- Mobile menu, show/hide based on menu state. -->
    <div x-show="mobileMenuOpen" @keydown.escape="mobileMenuOpen = false" class="md:hidden" id="mobile-menu" x-cloak>
        <div class="pt-2 pb-3 space-y-1 border-t border-gray-200 dark:border-zinc-700">
            <a href="{{ route('home') }}"
               class="{{ request()->routeIs('home') ? 'block pl-3 pr-4 py-2 border-l-4 border-indigo-500 text-base font-medium text-indigo-700 dark:text-indigo-400 bg-indigo-50 dark:bg-zinc-800 focus:outline-none focus:text-indigo-800 focus:bg-indigo-100 focus:border-indigo-700 transition' : 'block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-zinc-700 hover:border-gray-300 focus:outline-none focus:text-gray-800 focus:bg-gray-50 focus:border-gray-300 transition' }}">
                Inicio
            </a>
            <a href="{{ route('home') }}#funcionalidades"
               class="block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-zinc-700 hover:border-gray-300 focus:outline-none focus:text-gray-800 focus:bg-gray-50 focus:border-gray-300 transition">
                Funcionalidades
            </a>
            <a href="{{ route('home') }}#precios"
               class="block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-zinc-700 hover:border-gray-300 focus:outline-none focus:text-gray-800 focus:bg-gray-50 focus:border-gray-300 transition">
                Precios
            </a>
            <a href="{{ route('home') }}#faq"
               class="block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-zinc-700 hover:border-gray-300 focus:outline-none focus:text-gray-800 focus:bg-gray-50 focus:border-gray-300 transition">
                FAQ
            </a>
            @auth
                @if(Auth::user()->isWebsiteActivated())
                    <a href="{{ route('dashboard') }}"
                       class="{{ request()->routeIs('dashboard') ? 'block pl-3 pr-4 py-2 border-l-4 border-indigo-500 text-base font-medium text-indigo-700 dark:text-indigo-400 bg-indigo-50 dark:bg-zinc-800 focus:outline-none focus:text-indigo-800 focus:bg-indigo-100 focus:border-indigo-700 transition' : 'block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-zinc-700 hover:border-gray-300 focus:outline-none focus:text-gray-800 focus:bg-gray-50 focus:border-gray-300 transition' }}">
                        <strong>Dashboard</strong>
                    </a>
                @endif
                <a href="{{ route('activar') }}"
                   class="{{ request()->routeIs('activar') ? 'block pl-3 pr-4 py-2 border-l-4 border-indigo-500 text-base font-medium text-indigo-700 dark:text-indigo-400 bg-indigo-50 dark:bg-zinc-800 focus:outline-none focus:text-indigo-800 focus:bg-indigo-100 focus:border-indigo-700 transition' : 'block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-zinc-700 hover:border-gray-300 focus:outline-none focus:text-gray-800 focus:bg-gray-50 focus:border-gray-300 transition' }}">
                    @if(Auth::user()->isWebsiteActivated())
                        Mi Plan
                    @else
                        Activar Sistema
                    @endif
                </a>
                <a href="{{ route('settings.profile') }}"
                   class="block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-zinc-700 hover:border-gray-300 focus:outline-none focus:text-gray-800 focus:bg-gray-50 focus:border-gray-300 transition">
                    Configuración
                </a>
                <form method="POST" action="{{ route('logout') }}">
                    @csrf
                    <button type="submit"
                            class="block w-full text-left pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-zinc-700 hover:border-gray-300 focus:outline-none focus:text-gray-800 focus:bg-gray-50 focus:border-gray-300 transition">
                        Cerrar Sesión
                    </button>
                </form>
            @else
                <div class="pt-4 pb-3 border-t border-gray-200 dark:border-zinc-700">
                    <div class="mt-3 space-y-1">
                        <a href="{{ route('login') }}"
                           class="block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-zinc-700 hover:border-gray-300 focus:outline-none focus:text-gray-800 focus:bg-gray-50 focus:border-gray-300 transition">
                            Iniciar Sesión
                        </a>
                        <a href="{{ route('register') }}"
                           class="block pl-3 pr-4 py-2 border-l-4 border-indigo-500 text-base font-medium text-indigo-700 dark:text-indigo-400 bg-indigo-50 dark:bg-zinc-800 focus:outline-none focus:text-indigo-800 focus:bg-indigo-100 focus:border-indigo-700 transition">
                            Registrarse
                        </a>
                    </div>
                </div>
            @endauth
        </div>
    </div>
</header>

<main class="flex-grow">
    {{ $slot }}
</main>

<footer class="bg-indigo-900 text-white" aria-labelledby="footer-heading">
    <h2 id="footer-heading" class="sr-only">Footer</h2>
    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
        <div class="xl:grid xl:grid-cols-3 xl:gap-8">
            <div class="space-y-8 xl:col-span-1">
                <x-application-logo variant="white" class="h-10 w-auto"/>
                <p class="text-indigo-200 text-base">
                    Multibolsa Inmobiliaria facilita la gestión de inmuebles y la conexión con otros
                    profesionales del sector.
                </p>
                <div class="flex space-x-6">
                    <a href="https://www.facebook.com/multibolsainmobiliaria" target="_blank" class="text-indigo-200 hover:text-white">
                        <span class="sr-only">Facebook</span>
                        <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                                  clip-rule="evenodd"/>
                        </svg>
                    </a>
                    <a href="#" class="text-indigo-200 hover:text-white hidden">
                        <span class="sr-only">Instagram</span>
                        <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                                  clip-rule="evenodd"/>
                        </svg>
                    </a>
                    <a href="#" class="text-indigo-200 hover:text-white hidden">
                        <span class="sr-only">Twitter</span>
                        <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path
                                    d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                        </svg>
                    </a>
                </div>
            </div>
            <div class="mt-12 grid grid-cols-2 gap-8 xl:mt-0 xl:col-span-2">
                <div class="md:grid md:grid-cols-2 md:gap-8">
                    <div>
                        <h3 class="text-sm font-semibold text-indigo-100 tracking-wider uppercase hidden">
                            Soluciones
                        </h3>
                        <ul role="list" class="mt-4 space-y-4 hidden">
                            <li>
                                <a href="#" class="text-base text-indigo-200 hover:text-white">
                                    Multibolsa Inmobiliaria
                                </a>
                            </li>
                            <li>
                                <a href="#" class="text-base text-indigo-200 hover:text-white">
                                    Sitio Web Personalizado
                                </a>
                            </li>
                            <li>
                                <a href="#" class="text-base text-indigo-200 hover:text-white">
                                    Panel de Administración
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="mt-12 md:mt-0">
                        <h3 class="text-sm font-semibold text-indigo-100 tracking-wider uppercase">
                            Mulbin
                        </h3>
                        <ul role="list" class="mt-4 space-y-4">
                            <li>
                                <a href="{{ route('home') }}#funcionalidades" class="text-base text-indigo-200 hover:text-white">
                                    Funcionalidades
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('home') }}#precios" class="text-base text-indigo-200 hover:text-white">
                                    Precios
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('home') }}#faq" class="text-base text-indigo-200 hover:text-white">
                                    Preguntas Frecuentes
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="md:grid md:grid-cols-2 md:gap-8">
                    <div>
                        <h3 class="text-sm font-semibold text-indigo-100 tracking-wider uppercase hidden">
                            Empresa
                        </h3>
                        <ul role="list" class="mt-4 space-y-4 hidden">
                            <li>
                                <a href="#" class="text-base text-indigo-200 hover:text-white">
                                    Acerca de
                                </a>
                            </li>
                            <li>
                                <a href="#" class="text-base text-indigo-200 hover:text-white">
                                    Blog
                                </a>
                            </li>
                            <li>
                                <a href="#" class="text-base text-indigo-200 hover:text-white">
                                    Contacto
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="mt-12 md:mt-0">
                        <h3 class="text-sm font-semibold text-indigo-100 tracking-wider uppercase">
                            Legal
                        </h3>
                        <ul role="list" class="mt-4 space-y-4">
                            <li>
                                <a href="{{ route('privacidad') }}" class="text-base text-indigo-200 hover:text-white">
                                    Política de privacidad
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('terminos.uso') }}"
                                   class="text-base text-indigo-200 hover:text-white">
                                    Términos de uso
                                </a>
                            </li>
                            <li>
                                <a href="{{ route('eliminacion.datos') }}"
                                   class="text-base text-indigo-200 hover:text-white">
                                    Política de eliminación de datos
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-12 border-t border-indigo-800 pt-8">
            <p class="text-base text-indigo-200 xl:text-center">
                &copy; {{ date('Y') }} Multibolsa Inmobiliaria S.A. de C.V. Todos los derechos reservados.
            </p>
        </div>
    </div>
</footer>
@livewireScripts
@fluxScripts
</body>
</html> 