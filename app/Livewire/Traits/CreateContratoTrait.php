<?php

namespace App\Livewire\Traits;

use App\Models\BolsaInmobiliaria;
use App\Models\Cobro;
use App\Models\Contrato;
use App\Models\Plan;
use App\Models\Servicio;
use App\Models\SIConfig;
use App\Models\SIInvitation;
use App\Models\User;
use App\Services\CobroService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

trait CreateContratoTrait
{
    /**
     * Crea un contrato para el usuario basado en el plan seleccionado
     *
     * @param User $user El usuario para el que se crea el contrato
     * @param array $activationData Datos de activación del plan
     * @param array|null $invitationData Datos de la invitación (opcional)
     * @return Contrato|null El contrato creado
     */
    protected function createContrato(User $user, array $activationData, ?array $invitationData = null): ?Contrato
    {
        Log::info('ActivationForm: Creando contrato para usuario', [
            'user_id' => $user->id,
            'plan' => $activationData['selectedPlan'],
            'billing_period' => $activationData['billingPeriod']
        ]);

        // Obtener el servicio correspondiente al plan seleccionado
        if ($activationData['selectedPlan'] === 'SI-WEB') {
            $servicioCode = 'SI-WEB';
        } elseif ($activationData['selectedPlan'] === 'SI-PRO') {
            $servicioCode = 'SI-PRO';
        } elseif ($activationData['selectedPlan'] === 'SI-FREE' && $activationData['addMsComplement']) {
            // Si es plan FREE con complemento SI-MS, usamos el servicio SI-MS
            $servicioCode = 'SI-MS';
        } else { // freemium sin complemento
            $servicioCode = 'SI-FREE';
        }

        // Buscar el servicio en la base de datos
        $servicio = Servicio::where('servicio', $servicioCode)->first();

        // Si aún no se encuentra el servicio, crearlo
        if (!$servicio) {
            $servicio = $this->createServicioIfNotExists($servicioCode, $activationData);
        }

        if (!$servicio) {
            Log::error('ActivationForm: No se encontró el servicio para el plan', [
                'plan' => $activationData['selectedPlan'],
                'servicio_code' => $servicioCode
            ]);
            return null;
        }

        // Calcular la fecha de pago hasta basada en el período de facturación
        $pagadoHasta = now();

        // Determinar la frecuencia (frequency) basada en el período de facturación
        $frequency = match ($activationData['billingPeriod']) {
            'quarterly' => 3,
            'biannual' => 6,
            'annual' => 12,
            default => 1, // monthly
        };

        // Calcular la fecha de pago hasta
        $pagadoHasta = match ($activationData['billingPeriod']) {
            'quarterly' => $pagadoHasta->addMonths(3),
            'biannual' => $pagadoHasta->addMonths(6),
            'annual' => $pagadoHasta->addYear(),
            default => $pagadoHasta->addMonth(),
        };

        // Buscar si existe un plan con esta frecuencia
        $plan = Plan::where('frequency', $frequency)
            ->where('interval', 'month')
            ->where('is_active', true)
            ->first();

        // Determinar la forma de pago basada en la frecuencia
        // La forma_pago debe coincidir con la frecuencia del plan
        $formaPago = $frequency;

        Log::info('ActivationForm: Determinando forma de pago', [
            'billingPeriod' => $activationData['billingPeriod'],
            'frequency' => $frequency,
            'formaPago' => $formaPago,
            'plan_id' => $plan ? $plan->id : null
        ]);

        // Crear el contrato considerando el periodo elegido por el cliente
        $contrato = new Contrato([
            'usuario' => $user->usuario,
            'status' => true,
            'servicio' => $servicio->servicio,
            'dominio' => $user->usuario . '.mulbin.com',
            's_usuario' => $user->usuario,
            's_password' => Str::random(10),
            'observaciones' => 'Contrato creado automáticamente desde el proceso de activación',
            'forma_pago' => $formaPago, // Forma de pago basada en la frecuencia del plan
            'en_precio' => $this->calcularPorcentajeDescuento($activationData),
            'por_adelantado' => 'Si',
            'dias_tregua' => 5,
            'cobro_dia' => 0,
            'cobro_siguiente' => 5,
            'pago_automatizado' => true,
            'tipo' => 'particular',
            'fecha' => now(),
            // Para planes de pago, el pagado_hasta se establecerá cuando se confirme el pago
            // Para planes gratuitos sin complemento, se establece inmediatamente
            // Para planes gratuitos con complemento SI-MS, se establece cuando se confirme el pago
            'pagado_hasta' => ($activationData['selectedPlan'] === 'SI-FREE' && !$activationData['addMsComplement']) ? $pagadoHasta : null
        ]);

        $contrato->save();

        Log::info('ActivationForm: Contrato creado exitosamente', [
            'contrato_id' => $contrato->numero,
            'usuario' => $user->usuario,
            'servicio' => $servicio->servicio,
            'periodo_facturacion' => $activationData['billingPeriod'],
            'fecha_vencimiento' => $pagadoHasta->format('Y-m-d')
        ]);

        // Crear cobro inicial para el contrato usando el servicio CobroService
        $this->createCobroInicial($contrato, $user, $activationData, $pagadoHasta, $plan);

        // Crear la entrada en SIConfig
        $this->createSIConfigEntry($contrato, $user, $activationData);

        // Verifico en las invitaciones si existe una invitación pendiente para este usuario
        $invitation = SIInvitation::findAcceptedByEmail($user->logmail);
        if ($invitation) {
            $this->createBolsaInmobiliariaRelation($contrato, [
                'contrato_id' => $invitation->contrato_id,
                'invitation_id' => $invitation->id
            ]);
        }

//        // Crear relación en bolsa inmobiliaria si viene de invitación
//        if ($invitationData && isset($invitationData['contrato_id'])) {
//            $this->createBolsaInmobiliariaRelation($contrato, $invitationData);
//        }

        return $contrato;
    }

    /**
     * Crea un cobro inicial para el contrato
     *
     * @param Contrato $contrato El contrato para el que se crea el cobro
     * @param User $user El usuario asociado al contrato
     * @param array $activationData Datos de activación del plan
     * @param Carbon $pagadoHasta Fecha hasta la que se paga el servicio
     * @param Plan|null $plan El plan seleccionado (opcional)
     * @return Cobro|null El cobro creado o null si hubo un error
     */
    protected function createCobroInicial(
        Contrato $contrato,
        User $user,
        array $activationData,
        Carbon $pagadoHasta,
        ?Plan $plan = null
    ): ?Cobro {
        try {
            // Usar el servicio CobroService para crear el cobro inicial
            $cobroService = app(CobroService::class);

            return $cobroService->crearCobroInicial(
                $contrato,
                $user,
                $activationData,
                $pagadoHasta,
                $plan
            );
        } catch (Exception $e) {
            Log::error('ActivationForm: Error al crear cobro inicial', [
                'error' => $e->getMessage(),
                'contrato_id' => $contrato->numero
            ]);

            return null;
        }
    }

    /**
     * Crea un servicio si no existe
     *
     * @param string $servicioCode Código del servicio
     * @param array $activationData Datos de activación del plan
     * @return Servicio El servicio creado o encontrado
     */
    protected function createServicioIfNotExists(string $servicioCode, array $activationData): Servicio
    {
        Log::info('ActivationForm: Creando servicio que no existe', [
            'servicio_code' => $servicioCode
        ]);

        // Determinar el precio según el plan
        $precio = 0;
        if (isset($activationData['planPrice'])) {
            $precio = $activationData['planPrice'];
        } elseif ($servicioCode === 'SI-WEB') {
            $precio = 690; // Precio predeterminado para Web
        } elseif ($servicioCode === 'SI-PRO') {
            $precio = 1290; // Precio predeterminado para Pro
        }

        // Crear el servicio
        $servicio = new Servicio([
            'servicio' => $servicioCode,
            'descripcion' => 'Servicio ' . $servicioCode,
            'precio' => $precio,
            'notifica_vencimiento' => 1,
            'notifica_cada' => 1,
            'cobro_automatico' => 'Si',
            'cobro_mensual' => 'Si',
            'desc_trimestral' => 5, // 5% de descuento
            'desc_semestral' => 10, // 10% de descuento
            'desc_anual' => 15, // 15% de descuento
            'renovacion_cada' => 1, // Renovación mensual
            'inmuebles' => 0, // Sin límite de inmuebles
            'publicado' => 'Si',
            'acepta_prorrogas' => 'Si',
            'NotificandoVencidos' => 1,
            'QueHacerVencido' => 1,
            'tipo' => 'web'
        ]);

        $servicio->save();

        Log::info('ActivationForm: Servicio creado exitosamente', [
            'servicio_id' => $servicio->id,
            'servicio_code' => $servicioCode
        ]);

        return $servicio;
    }

    /**
     * Calcula el porcentaje de descuento basado en el período de facturación
     *
     * @param array $activationData Datos de activación del plan
     * @return float Porcentaje de descuento (negativo) o incremento (positivo)
     */
    protected function calcularPorcentajeDescuento(array $activationData): float
    {
        // Obtener el descuento según el período de facturación
        $descuento = match ($activationData['billingPeriod']) {
            'quarterly' => -5, // 5% de descuento
            'biannual' => -10, // 10% de descuento
            'annual' => -15, // 15% de descuento
            default => 0, // Sin descuento para mensual
        };

        Log::info('ActivationForm: Calculando porcentaje de descuento', [
            'billingPeriod' => $activationData['billingPeriod'],
            'descuento' => $descuento
        ]);

        return $descuento;
    }

    /**
     * Crea la entrada en la tabla config para el nuevo sitio web
     *
     * @param Contrato $contrato El contrato asociado a la configuración
     * @param User $user El usuario para el que se crea la configuración
     * @param array $activationData Datos de activación del plan
     * @return void
     */
    protected function createSIConfigEntry(Contrato $contrato, User $user, array $activationData): void
    {
        try {
            Log::info('ActivationForm: Creando entrada en SIConfig', [
                'contrato_id' => $contrato->numero,
                'usuario' => $user->usuario
            ]);

            // Configurar la ruta según el formato de clientes activos
            $rutaUsuario = '/home/<USER>/' . $user->usuario . '/';

            // Calcular la fecha de vencimiento basada en el período de facturación
            // Si el contrato ya tiene pagado_hasta, usamos esa fecha
            // Si no, calculamos la fecha basada en el período de facturación
            if ($contrato->pagado_hasta) {
                $vencimiento = $contrato->pagado_hasta;
            } else {
                $vencimiento = now();
                $vencimiento = match ($activationData['billingPeriod']) {
                    'quarterly' => $vencimiento->addMonths(3),
                    'biannual' => $vencimiento->addMonths(6),
                    'annual' => $vencimiento->addYear(),
                    default => $vencimiento->addMonth(),
                };
            }

            // Crear o actualizar la configuración
            SIConfig::updateOrCreate(['contrato' => $contrato->numero], [
                'usuario' => $user->usuario,
                'correo_ventas' => $user->email,
                'correo_contratacion' => $user->email,
                'correo_comentarios' => $user->email,
                'correo_dirgral' => $user->email,
//                'dominio' => $contrato->dominio,
//                'dominio2' => null,
                'redes_sociales' => json_encode([
                    'facebook' => '',
                    'twitter' => '',
                    'instagram' => '',
                    'youtube' => '',
                    'linkedin' => ''
                ]),
                'ruta' => $rutaUsuario,
                'foto_ancho' => 425, // Valor alineado con el ejemplo
                'por_ciudades' => 'No', // Ajustado según ejemplo
                'prop_por_pag' => 12, // Ajustado según ejemplo
                'prop_por_linea' => 1,
                'tipo_tema' => 'Personalizado',
                'theme' => 'Default', // Inicialmente Default, se personalizará después
                'theme_hash' => null,
                'theme_dev' => '', // Cadena vacía en lugar de null
                'nueva_ventana' => 'No', // Ajustado según ejemplo
                'ligas_comunes' => 'No', // Ajustado según ejemplo
                'ultimo_acceso' => now(),
                'combinar_ampi' => 'No',
                'notifica_telefono' => 'No', // Ajustado según ejemplo
                'telefono_props' => 'No',
                'transparencia_fotos' => 'Si',
                'tipo_fotos' => 'ambos',
                'fotos_por_linea' => 9, // Ajustado según ejemplo
                'tipo_galeria' => 'dinamica',
                'socio_ampi' => 'No',
                'funcion_ampi' => 0,
                'ampi_admin' => 'No',
                'estado' => $user->estado,
                'orden_inmuebles' => 'fecha',
                'forma_orden_inmuebles' => 'descendente',
                'enviar_nextel' => 'No', // Ajustado según ejemplo
                'fotos' => 'otro',
                'moneda_predeterminada' => 'MXP',
                'mostrar_monedas' => 'MXP,USD',
                'idioma' => 'esp',
                'campos_por_linea' => 2,
                'leyenda_b_favoritos' => 'Si',
                'lat_club_negocios' => 'Si',
                'ultimo_cobro' => now()->format('Y-m-d'),
                'dias_novedades' => 30,
                'notificar_propietarios' => 'No',
                'detalles_new' => 'No',
                'status' => 1,
                'servicio_contratado' => $contrato->servicio,
                'pagado_hasta' => $contrato->pagado_hasta ? $contrato->pagado_hasta->format(
                    'Y-m-d'
                ) : $vencimiento->format('Y-m-d'),
                'vencido_si' => 'No',
                'vencimiento' => $vencimiento->format('Y-m-d'),
                'prorroga' => null,
                'saldo' => 0.00,
                'adeudo' => 0.00,
                'a_vencer' => 0.00,
                'saldo_a_favor' => 0.00,
                'credito' => 0.00,
                'msg_panel_desactivado' => null,
                'presentar_con' => 'fotos',
                'front' => '0', // Inicialmente inactivo hasta que se complete la activación
                'deptID_livephp' => null, // Será asignado más tarde si se necesita
                'custom_data' => json_encode([
                    'plan' => $activationData['selectedPlan'],
                    'billing_period' => $activationData['billingPeriod'],
                    'activation_date' => now()->format('Y-m-d H:i:s'),
                    'addMsComplement' => $activationData['addMsComplement'] ?? false,
                    'msComplementPrice' => $activationData['msComplementPrice'] ?? 0,
                    'msComplementName' => $activationData['msComplementName'] ?? '',
                    'msComplementDescription' => $activationData['msComplementDescription'] ?? '',
                    'msComplementFeatures' => $activationData['msComplementFeatures'] ?? []
                ]),
                'external_connections' => null, // Inicialmente null como en el ejemplo
                'created_at' => now(),
                'updated_at' => now()
            ]);

            Log::info('ActivationForm: Entrada en SIConfig creada exitosamente', [
                'contrato_id' => $contrato->numero,
                'usuario' => $user->usuario,
                'dominio' => $contrato->dominio,
                'ruta' => $rutaUsuario
            ]);
        } catch (Exception $e) {
            Log::error('ActivationForm: Error al crear entrada en SIConfig', [
                'error' => $e->getMessage(),
                'contrato_id' => $contrato->numero
            ]);
        }
    }

    /**
     * Crea la relación en la bolsa inmobiliaria entre el contrato invitador y el invitado
     *
     * @param Contrato $contratoInvitado El contrato del usuario que fue invitado
     * @param array $invitationData Datos de la invitación
     * @return void
     */
    protected function createBolsaInmobiliariaRelation(Contrato $contratoInvitado, array $invitationData): void
    {
        try {
            $contratoSolicitante = $invitationData['contrato_id'];
            $contratoSolicitado = $contratoInvitado->numero;

            Log::info('ActivationForm: Creando relación en bolsa inmobiliaria', [
                'contrato_solicitante' => $contratoSolicitante,
                'contrato_solicitado' => $contratoSolicitado,
                'invitation_id' => $invitationData['invitation_id'] ?? null
            ]);

            // Verificar que no exista ya una relación
            if (BolsaInmobiliaria::existeRelacion($contratoSolicitante, $contratoSolicitado)) {
                Log::warning('ActivationForm: La relación ya existe en bolsa inmobiliaria', [
                    'contrato_solicitante' => $contratoSolicitante,
                    'contrato_solicitado' => $contratoSolicitado
                ]);
                return;
            }

            // Crear la relación en la bolsa inmobiliaria
            $relacion = BolsaInmobiliaria::crearRelacion($contratoSolicitante, $contratoSolicitado);

            Log::info('ActivationForm: Relación en bolsa inmobiliaria creada exitosamente', [
                'relacion_id' => $relacion->id,
                'contrato_solicitante' => $contratoSolicitante,
                'contrato_solicitado' => $contratoSolicitado,
                'fecha_solicitud' => $relacion->fecha_solicitud,
                'fecha_aceptacion' => $relacion->fecha_aceptacion
            ]);
        } catch (Exception $e) {
            Log::error('ActivationForm: Error al crear relación en bolsa inmobiliaria', [
                'error' => $e->getMessage(),
                'contrato_solicitante' => $invitationData['contrato_id'] ?? null,
                'contrato_solicitado' => $contratoInvitado->numero,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}