<?php

namespace App\Livewire\Traits;

use App\Services\PaymentService;
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

trait ActivationFormSubmitTrait
{
    use CreateContratoTrait;

    protected $rules = [
        'selectedPlan' => 'required|string',
        'billingPeriod' => 'required|string',
        'username' => 'required|string|min:3|max:30|alpha_dash',
        'empresa' => 'required|string|max:50',
        'phone_country_code' => 'required|string|max:5',
        'phone_number' => 'required|string|max:20',
        'calle_numero' => 'required|string|max:70',
        'colonia' => 'required|string|max:30',
        'codigo_postal' => 'required|string|max:5',
        'ciudad' => 'required|string|max:30',
        'estado' => 'required|string|max:25',
        'pais' => 'required|string|max:25'
    ];

    protected $messages = [
        'selectedPlan.required' => 'Por favor seleccione un plan.',
        'billingPeriod.required' => 'Por favor seleccione un período de facturación.',
        'cardNumber.required_if' => 'El número de tarjeta es obligatorio para este plan.',
        'cardName.required_if' => 'El nombre del titular es obligatorio para este plan.',
        'cardExpiry.required_if' => 'La fecha de expiración es obligatoria para este plan.',
        'cardCvc.required_if' => 'El código de seguridad es obligatorio para este plan.',
        'username.required' => 'Por favor ingresa un nombre de usuario',
        'username.min' => 'El nombre de usuario debe tener al menos 3 caracteres',
        'username.max' => 'El nombre de usuario no puede tener más de 30 caracteres',
        'username.alpha_dash' => 'El nombre de usuario solo puede contener letras, números, guiones y guiones bajos',
        'username.unique' => 'El nombre de usuario ya está en uso',
        'empresa.required' => 'Por favor ingresa el nombre de la agencia',
        'empresa.max' => 'El nombre de la agencia no puede tener más de 50 caracteres',
        'phone_country_code.required' => 'Por favor selecciona un código de país',
        'phone_number.required' => 'Por favor ingresa tu número de teléfono',
        'calle_numero.required' => 'Por favor ingresa la calle y número',
        'colonia.required' => 'Por favor ingresa la colonia',
        'codigo_postal.required' => 'Por favor ingresa el código postal',
        'ciudad.required' => 'Por favor ingresa la ciudad',
        'estado.required' => 'Por favor ingresa el estado',
        'pais.required' => 'Por favor ingresa el país',
    ];

    /**
     * Envía el formulario de activación
     */
    public function submit(): RedirectResponse|null
    {
        Log::info('ActivationForm: Iniciando submit', [
            'selectedPlan' => $this->selectedPlan,
            'billingPeriod' => $this->billingPeriod,
            'conektaTokenId' => $this->conektaTokenId ? 'presente' : 'ausente',
            'username' => $this->username
        ]);

        $user = Auth::user();

        // Verificar si el usuario ya tiene su sitio web activado
        if ($user->isWebsiteActivated()) {
            // Si ya está activado, redirigir a la página de activación exitosa
            return $this->redirect(route('activacion.exitosa'));
        }

        // Verificar disponibilidad del nombre de usuario
        $this->performUsernameCheck();
        if (!$this->usernameAvailable) {
            $this->addError('username', 'El nombre de usuario ya está en uso');
            return null;
        }

        // Si es plan freemium sin complemento, no validamos el conektaTokenId
        // Si es plan freemium con complemento SI-MS, sí validamos el conektaTokenId
        if ($this->selectedPlan === 'SI-FREE' && !$this->addMsComplement) {
            $this->validate();
        } else {
            $this->validate(
                array_merge($this->rules, [
                    'conektaTokenId' => 'required_if:selectedPlan,web,pro|nullable|string',
                ])
            );
        }
        Log::info('ActivationForm: Validación exitosa');

        // Guardar los datos del plan en la sesión
        $activationData = [
            'selectedPlan' => $this->selectedPlan,
            'billingPeriod' => $this->billingPeriod,
            'planPrice' => $this->planPrice,
            'totalPrice' => $this->totalPrice,
            'addMsComplement' => $this->addMsComplement,
            'msComplementPrice' => $this->msComplementPrice,
            'msComplementName' => $this->msComplementName,
            'msComplementDescription' => $this->msComplementDescription,
            'msComplementFeatures' => $this->msComplementFeatures,
            'token_id' => (($this->selectedPlan !== 'SI-FREE' || $this->addMsComplement) && $this->conektaTokenId) ? $this->conektaTokenId : null,
        ];

        // Guardar los datos del usuario en la sesión
        $userData = [
            'username' => $this->username,
            'empresa' => $this->empresa,
            'phone_country_code' => $this->phone_country_code,
            'phone_number' => $this->phone_number,
            'calle_numero' => $this->calle_numero,
            'colonia' => $this->colonia,
            'codigo_postal' => $this->codigo_postal,
            'ciudad' => $this->ciudad,
            'estado' => $this->estado,
            'pais' => $this->pais,
        ];

        session(['activation_data' => $activationData, 'user_data' => $userData]);
        Log::info('ActivationForm: Datos guardados en sesión', [
            'activationData' => $activationData,
            'userData' => $userData
        ]);

        // Procesar la activación directamente en el componente
        Log::info('ActivationForm: Procesando activación directamente');

        // Guardar la información del plan seleccionado en la sesión para mostrarla en la página de éxito
        session([
            'plan' => $activationData['selectedPlan'],
            'billing_period' => $activationData['billingPeriod'],
            'plan_price' => $activationData['planPrice'],
            'total_price' => $activationData['totalPrice'],
        ]);

        // Si es plan freemium sin complemento, actualizamos los datos del usuario y activamos el portal
        if ($activationData['selectedPlan'] === 'SI-FREE' && !$activationData['addMsComplement']) {
            Log::info('ActivationForm: Procesando plan freemium sin complemento');

            // Actualizamos el nombre de usuario, empresa, teléfono, dirección y activamos el portal para dentro de 24 horas
            $this->updateUserData($user);

            Log::info('ActivationForm: Portal activado para usuario', ['user_id' => $user->id]);

            // Crear contrato para el usuario con plan freemium
            $this->createContrato($user, $activationData);

            // Limpiar los datos de activación y usuario de la sesión
            session()->forget(['activation_data', 'user_data']);
            Log::info('ActivationForm: Datos eliminados de la sesión');

            return $this->redirect(route('activacion.exitosa'));
        }

        // Si es plan freemium con complemento SI-MS, procesamos como un plan de pago
        if ($activationData['selectedPlan'] === 'SI-FREE' && $activationData['addMsComplement']) {
            Log::info('ActivationForm: Procesando plan freemium con complemento SI-MS');
            // Continuamos con el proceso de pago para el complemento
        }

        // Si es un plan de pago, procesamos con Conekta
        $paymentService = app(PaymentService::class);

        try {
            Log::info('ActivationForm: Procesando plan pagado', [
                'plan' => $activationData['selectedPlan'],
                'periodo' => $activationData['billingPeriod']
            ]);

            // Validar que el token_id esté presente
            if (empty($activationData['token_id'])) {
                Log::warning('ActivationForm: Token de tarjeta no proporcionado');
                return back()->withErrors(
                    ['token_id' => 'No se ha proporcionado un token válido para la tarjeta. Por favor valide su tarjeta primero.']
                );
            }

            Log::info('ActivationForm: Token de tarjeta presente', [
                'token' => substr($activationData['token_id'], 0, 5) . '...'
            ]);

            // Mapear los IDs de planes según corresponda en Conekta
            // Asegurarse de que el período de facturación sea compatible con los planes creados
            $billingPeriodMap = [
                'monthly' => 'monthly',
                'quarterly' => 'quarterly',
                'biannual' => 'biannual',  // Asegurarse de que coincida con el ID en ConektaPlansController
                'annual' => 'annual'
            ];

            $mappedBillingPeriod = $billingPeriodMap[$activationData['billingPeriod']] ?? 'monthly';

            // Si es plan FREE con complemento SI-MS, usamos el ID del plan SI-MS
            if ($activationData['selectedPlan'] === 'SI-FREE' && $activationData['addMsComplement']) {
                $planConektaId = 'plan-SI-MS-' . $mappedBillingPeriod;
            } else {
                $planConektaId = 'plan-' . $activationData['selectedPlan'] . '-' . $mappedBillingPeriod;
            }
            Log::info('ActivationForm: Plan Conekta ID generado', ['planConektaId' => $planConektaId]);

            // Crear cliente y suscripción en Conekta
            Log::info('ActivationForm: Llamando a createAndSubscribeCustomer', [
                'email' => $user->email,
                'name' => $user->name
            ]);

            $result = $paymentService->createAndSubscribeCustomer(
                $activationData['token_id'],
                $user->email,
                $user->name,
                $planConektaId
            );

            Log::info('ActivationForm: Resultado de createAndSubscribeCustomer', [
                'success' => !isset($result['error']),
                'error' => $result['error'] ?? null,
                'hasCustomer' => isset($result['Customer']),
                'hasSubscription' => isset($result['Subscription'])
            ]);

            // Verificar si hubo error
            if (isset($result['error'])) {
                Log::error('ActivationForm: Error al crear cliente/suscripción en Conekta: ' . $result['error']);
                return back()->withErrors(['general' => 'Error al procesar el pago: ' . $result['error']]);
            }

            // Verificar que los datos necesarios estén presentes
            if (!isset($result['Customer']) || !isset($result['Customer']['id']) || !isset($result['Subscription']) || !isset($result['Subscription']['id'])) {
                Log::error('ActivationForm: Respuesta incompleta de Conekta: ' . json_encode($result));
                return back()->withErrors(
                    ['general' => 'Error al procesar el pago: respuesta incompleta del servidor de pagos.']
                );
            }

            // Guardar el ID de la suscripción en los datos de activación para usarlo en el cobro
            $activationData['subscription_id'] = $result['Subscription']['id'];

            // Actualizar los datos de activación en la sesión
            session(['activation_data' => $activationData]);

            Log::info('ActivationForm: ID de suscripción guardado', [
                'subscription_id' => $activationData['subscription_id']
            ]);

            // Guardar la información del cliente en nuestra base de datos
            $this->updateOrCreateConektaCustomer($user, $result, $activationData, $planConektaId);
        } catch (Exception $e) {
            Log::error('ActivationForm: Excepción al procesar pago con Conekta: ' . $e->getMessage());
            return back()->withErrors(['general' => 'Error inesperado al procesar el pago. Por favor intente de nuevo.']
            );
        }

        // Actualizamos el nombre de usuario, empresa, teléfono, dirección y activamos el portal para dentro de 24 horas
        $this->updateUserData($user);

        // Obtener datos de invitación si existen
        $invitationData = session('invitation_data_for_activation');

        // Crear contrato para el usuario
        $this->createContrato($user, $activationData, $invitationData);

        // Limpiar los datos de activación, usuario e invitación de la sesión
        session()->forget(['activation_data', 'user_data', 'invitation_data_for_activation']);

        // Redirigir a la página de activación exitosa
        return $this->redirect(route('activacion.exitosa'));
    }
}