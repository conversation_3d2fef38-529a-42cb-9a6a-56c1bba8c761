<?php

namespace App\Livewire;

use App\Livewire\Traits\ActivationFormSubmitTrait;
use App\Models\User;
use App\Traits\UpdateOrCreateConektaCustomerTrait;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Livewire\Component;

class ActivationForm extends Component
{
    use UpdateOrCreateConektaCustomerTrait,
        ActivationFormSubmitTrait;

    // Variables para el plan seleccionado
    public $selectedPlan = null;
    public $billingPeriod = 'monthly';
    public $planPrice = 0;
    public $totalPrice = 0;
    public $addMsComplement = false;
    public $msComplementPrice = 0;
    public $msComplementName = '';
    public $msComplementDescription = '';
    public $msComplementFeatures = [];

    // Variables para tarjeta de crédito/débito (solo para UI)
    public $cardNumber;
    public $cardName;
    public $cardExpiry;
    public $cardCvc;

    // Variables para el nombre de usuario, teléfono y dirección
    public string $username = '';
    public string $empresa = ''; // Almacena el valor original del nombre de la agencia
    public ?bool $usernameAvailable = null;
    public bool $checkingUsername = false;
    public string $phone_country_code = '';
    public string $phone_number = '';

    // Variables para la dirección con Google Places Autocomplete
    public string $calle_numero = '';
    public string $colonia = '';
    public string $codigo_postal = '';
    public string $ciudad = '';
    public string $estado = '';
    public string $pais = 'México';

    // Token de Conekta
    public ?string $conektaTokenId = null;

    // Estado del formulario
    public int $currentStep = 1; // 1: Selección de plan, 2: Datos de pago

    /**
     * Listeners para eventos
     *
     * @return string[]
     */
    protected function getListeners(): array
    {
        return [
            'planSelected' => 'handlePlanSelected',
            'tokenCreated' => 'handleTokenCreated',
            'username-processed' => 'handleUsernameProcessed',
            'card-validated' => 'handleCardValidated'
        ];
    }

    /**
     * Hook que se ejecuta al montar el componente
     */
    public function mount(): void
    {
        $user = Auth::user();

        // Inicializar el nombre de usuario con el valor actual del usuario
//        $this->username = $user->usuario;

        // Inicializar la empresa con el valor actual del usuario
        $this->empresa = empty($user->empresa) ? $user->usuario : $user->empresa;

        // Inicializar el teléfono si existe
        if (!empty($user->telefono)) {
            // Intentar separar el código de país y el número
            $phone_parts = explode(' ', $user->telefono, 2);
            if (count($phone_parts) == 2) {
                $this->phone_country_code = $phone_parts[0];
                $this->phone_number = $phone_parts[1];
            } else {
                // Si no tiene formato esperado, asignar todo al número
                $this->phone_number = $user->telefono;
                $this->phone_country_code = '+52'; // Valor predeterminado
            }
        } else {
            // Valores predeterminados
            $this->phone_country_code = '+52';
            $this->phone_number = '';
        }

        // Inicializar campos de dirección
        $this->calle_numero = $user->calle_numero ?? '';
        $this->colonia = $user->colonia ?? '';
        $this->codigo_postal = empty($user->codigo_postal) ? '' : (string)$user->codigo_postal;
        $this->ciudad = $user->ciudad ?? '';
        $this->estado = $user->estado ?? '';
        $this->pais = empty($user->pais) ? 'México' : $user->pais;
    }

    /**
     * Maneja el evento cuando se selecciona un plan
     */
    public function handlePlanSelected($data): void
    {
        $this->selectedPlan = $data['plan'];
        $this->billingPeriod = $data['billingPeriod'];
        $this->planPrice = $data['price'];
        $this->totalPrice = $data['totalPrice'];

        // Manejar el complemento SI-MS si está presente
        $this->addMsComplement = $data['addMsComplement'] ?? false;
        $this->msComplementPrice = $data['msComplementPrice'] ?? 0;
        $this->msComplementName = $data['msComplementName'] ?? 'Complemento Micrositio';
        $this->msComplementDescription = $data['msComplementDescription'] ?? '';
        $this->msComplementFeatures = $data['msComplementFeatures'] ?? [];

        // Si es plan FREE con complemento SI-MS, necesitamos mostrar el formulario de pago
        // aunque sea un plan gratuito, porque el complemento sí requiere pago

        // Avanzamos al siguiente paso
        $this->nextStep();
    }

    /**
     * Maneja el evento cuando se crea un token de Conekta
     */
    public function handleTokenCreated($tokenId): void
    {
        // Asegurarse de que tokenId sea un string
        if (is_array($tokenId)) {
            $this->conektaTokenId = $tokenId[0] ?? '';
        } else {
            $this->conektaTokenId = $tokenId;
        }
    }

    /**
     * Format username input to valid format (server-side fallback)
     */
    public function updatedUsername($value): void
    {
        $this->validate([
            'username' => 'required',
        ]);

        // Asegurar que value sea un string
        if (!is_string($value)) {
            $value = (string)$value;
            $this->username = $value;
        }

        // Guardar el valor original en la propiedad empresa
        $this->empresa = $value;

        // Convertir a slug
        $this->username = Str::slug($value);

        // Verificar disponibilidad
        $this->checkUsernameAvailability();

        // Emitir evento para que la vista actualice el input
        $this->dispatch('username-processed', username: $this->username);
    }

    public function updatedPhoneCountryCode($value): void
    {
        Log::info('phone_country_code: ' . $value);
        $this->validate([
            'phone_country_code' => 'required',
        ]);
        $this->phone_country_code = $value;
    }

    public function updatedPhoneNumber($value): void
    {
        Log::info('phone_number: ' . $value);
        $this->validate([
            'phone_number' => 'required',
        ]);
        $this->phone_number = $value;
    }

    /**
     * Handle username processed event
     */
    public function handleUsernameProcessed($username): void
    {
        if (is_array($username)) {
            $this->username = $username['username'] ?? $this->username;
            // Si recibimos el valor original, lo guardamos en empresa
            if (isset($username['original'])) {
                $this->empresa = $username['original'];
            }
        } else {
            $this->username = $username;
        }
    }

    /**
     * Check if the username is available
     */
    public function checkUsernameAvailability(): void
    {
        if (empty($this->username)) {
            $this->usernameAvailable = null;
            return;
        }

        $this->checkingUsername = true;
        $this->usernameAvailable = null;

        // Verificamos directamente
        $this->performUsernameCheck();
    }

    /**
     * Perform the actual username availability check
     */
    public function performUsernameCheck(): void
    {
        if (empty($this->username)) {
            $this->checkingUsername = false;
            $this->usernameAvailable = null;
            return;
        }

        // Asegurar que username sea un string
        $this->username = (string)$this->username;

        // Verificamos si el nombre de usuario ya existe en la base de datos
        // Excluimos el usuario actual para permitir mantener el mismo nombre
        $user = Auth::user();
        $exists = User::where('usuario', $this->username)
            ->where('id', '!=', $user->id)
            ->exists();
        $this->usernameAvailable = !$exists;
        $this->checkingUsername = false;
    }

    /**
     * Avanza al siguiente paso del formulario
     */
    public function nextStep(): void
    {
        $this->currentStep++;

        // Si avanzamos al paso del formulario, emitimos un evento para activar el scroll
        if ($this->currentStep == 2) {
            $this->dispatch('formStepReady');
        }
    }

    /**
     * Retrocede al paso anterior del formulario
     */
    public function previousStep(): void
    {
        $this->currentStep--;
    }

    public function render(): View
    {
        return view('livewire.activation-form');
    }

    /**
     * Actualiza los datos del usuario
     *
     * @param User $user
     * @return void
     */
    private function updateUserData(User $user): void
    {
        $user->usuario = $this->username;
        $user->empresa = $this->empresa; // Guardamos el valor original del nombre de la agencia
        $user->telefono = $this->phone_country_code . ' ' . $this->phone_number;
        $user->calle_numero = $this->calle_numero;
        $user->colonia = $this->colonia;
        $user->codigo_postal = $this->codigo_postal;
        $user->ciudad = $this->ciudad;
        $user->estado = $this->estado;
        $user->pais = $this->pais;
        $user->website_activated_at = now()->addHours(24);
        $user->save();
    }

    /**
     * Maneja el evento cuando el componente ConektaCardForm valida la tarjeta
     */
    public function handleCardValidated($tokenId): void
    {
        $this->conektaTokenId = $tokenId;
    }
}