<?php

namespace App\Livewire;

use Livewire\Component;

class DashboardAndWebsiteAccess extends Component
{
    public string $websiteUrl = '';
    public string $dashboardUrl = '';

    public function mount()
    {
        // Cargar la relación SIConfig para evitar consultas adicionales
        $user = auth()->user()->load('siConfig');

        $this->websiteUrl = $user->getWebsiteUrl();
        $this->dashboardUrl = $user->getPanelUrl();
    }

    public function render()
    {
        return view('livewire.dashboard-and-website-access');
    }
}
