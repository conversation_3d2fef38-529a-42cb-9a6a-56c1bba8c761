<?php

namespace App\Models;

use App\Notifications\ResetPasswordNotification;
use App\Notifications\VerifyEmailNotification;
use Database\Factories\UserFactory;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;

/**
 * Usaré la tabla 'clientes'
 */
class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The column name that should be used for authentication.
     *
     * @var string
     */
//    protected string $username = 'logmail';
    protected $connection = 'publiweb';
    protected $table = 'clientes';

//    public function __construct(array $attributes = [])
//    {
//        parent::__construct($attributes);
//        $this->connection = 'publiweb';
//        $this->table = 'clientes';
//    }

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'logmail',
        'name',
        'nombre',
        'apellidos',
        'usuario',
        'email',
        'email_verified_at',
        'pending_email',
        'pending_email_at',
        'calle_numero',
        'colonia',
        'codigo_postal',
        'ciudad',
        'estado',
        'pais',
        'phone_country_code',
        'phone_number',
        'telefono',
        'password',
        'google_id',
        'google_token',
        'google_refresh_token',
        'facebook_id',
        'facebook_token',
        'avatar',
        'quien_registro',
        'activo',
        'empresa',
        'celular',
        'email_sec',
        'sitio_web',
        'fact_nombre',
        'fact_domicilio',
        'fact_rfc',
        'pabusqueda'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    public function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'pending_email_at' => 'datetime',
            'password' => 'hashed',
            'website_activated_at' => 'datetime',
        ];
    }

    /**
     * Get the user's initials
     */
    public function initials(): string
    {
        // Si tenemos nombre y apellidos disponibles, usamos esos
        if (!empty($this->nombre) && !empty($this->apellidos)) {
            return strtoupper(substr($this->nombre, 0, 1) . substr($this->apellidos, 0, 1));
        }

        // Si no, continuamos con el método anterior usando name
        return Str::of($this->name)
            ->explode(' ')
            ->map(fn(string $name) => Str::of($name)->substr(0, 1))
            ->implode('');
    }

    /**
     * Get the user's SIConfig relationship
     */
    public function siConfig()
    {
        return $this->hasOne(SIConfig::class, 'usuario', 'usuario');
    }

    /**
     * Get the user's website URL
     */
    public function getWebsiteUrl(): string
    {
        if (empty($this->usuario)) {
            return '';
        }

        // Usar la relación para obtener la configuración
        $siConfig = $this->siConfig;

        if ($siConfig && !empty($siConfig->dominio)) {
            // Si tiene dominio personalizado, usar ese dominio
            return 'https://' . $siConfig->dominio;
        }

        // Si no tiene dominio personalizado, usar el subdominio por defecto
        return 'https://' . $this->usuario . '.web.mulbin.com';
    }

    /**
     * Get the user's panel URL
     */
    public function getPanelUrl(): string
    {
        if (empty($this->usuario)) {
            return '';
        }

        // Usar la relación para obtener la configuración
        $siConfig = $this->siConfig;

        if ($siConfig && !empty($siConfig->dominio)) {
            // Si tiene dominio personalizado, usar panel.{dominio}
            return 'https://panel.' . $siConfig->dominio;
        }

        // Si no tiene dominio personalizado, usar el subdominio por defecto
        return 'https://' . $this->usuario . '.panel.' . getenv('MULBIN_DOMAIN');
    }

    public function getField(string $field): string
    {
        return $this->$field;
    }

    /**
     * Set the nombre and sync with name
     */
    public function setFirstnameAttribute($value): void
    {
        $this->attributes['nombre'] = $value;
        $this->syncNameFromParts();
    }

    /**
     * Set the apellidos and sync with name
     */
    public function setLastnameAttribute($value): void
    {
        $this->attributes['apellidos'] = $value;
        $this->syncNameFromParts();
    }

    /**
     * Sync name field from nombre and apellidos
     */
    protected function syncNameFromParts(): void
    {
        if (isset($this->attributes['nombre']) && isset($this->attributes['apellidos'])) {
            $this->attributes['name'] = trim($this->attributes['nombre'] . ' ' . $this->attributes['apellidos']);
        }
    }

    /**
     * Check if the user's website is activated
     *
     * A website is considered activated if either:
     * 1. The website_activated_at field is not null, or
     * 2. The user has a contract (active or not) with a service of type 'PLAN-SI'
     *
     * @return bool
     */
    public function isWebsiteActivated(): bool
    {
        // Check if website_activated_at is set
        if (!is_null($this->website_activated_at)) {
            return true;
        }
//        return false;

        // Check if the user has any contract with a service of type 'PLAN-SI'
        return $this->contratos()
            ->whereHas('servicioRelacion', function ($query) {
                $query->where('tipo', 'PLAN-SI');
            })->exists();
    }

    /**
     * Get the user's avatar URL
     */
    public function getAvatarUrl(): ?string
    {
        return $this->avatar;
    }

    /**
     * Get the user's avatar URL for Flux components
     */
    public function getFluxAvatarUrl(): ?string
    {
        return $this->getAvatarUrl();
    }

    /**
     * Get the login identifier for the user.
     *
     * @return string
     */
    public function getAuthIdentifierName(): string
    {
        return 'logmail';
    }

    /**
     * Send the password reset notification.
     *
     * @param string $token
     * @return void
     */
    public function sendPasswordResetNotification($token): void
    {
        $this->notify(new ResetPasswordNotification($token));
    }

    /**
     * Send the email verification notification.
     *
     * @return void
     */
    public function sendEmailVerificationNotification(): void
    {
        $this->notify(new VerifyEmailNotification);
    }

    /**
     * Get the email address that should be used for verification.
     *
     * @return string
     */
    public function getEmailForVerification(): string
    {
        return $this->email;
    }

    /**
     * Método original de encriptación usado en el sistema anterior
     * Mantenido para compatibilidad con contraseñas antiguas.
     *
     * @param string $str La contraseña a encriptar
     * @param string|null $salt Salt opcional para la encriptación
     * @return string La contraseña encriptada en formato salt:hash
     */
    private function oldCrypt(string $str, ?string $salt = null): string
    {
        if (!$salt) {
            $salt = md5(uniqid(rand(), 1));
        }
        $salt = substr($salt, 0, 8);
        return $salt . ":" . md5($salt . $str);
    }

    /**
     * Verifica si la contraseña utiliza el algoritmo Bcrypt o necesita actualización.
     *
     * @param string $password La contraseña a verificar
     * @return bool Verdadero si la contraseña es válida (verificación alternativa para contraseñas antiguas)
     */
    public function checkLegacyPassword(string $password): bool
    {
        // Si la contraseña ya está en formato Bcrypt (60 caracteres comenzando con $2y$)
        if (strlen($this->password) === 60 && str_starts_with($this->password, '$2y$')) {
            return false; // No es una contraseña antigua, usa el verificador normal
        }

        // Verificar si es formato antiguo personalizado (salt:hash)
        return
            str_contains($this->password, ':') &&
            $this->oldCrypt($password, substr($this->password, 0, 8)) == $this->password;
    }

    /**
     * Obtiene los contratos asociados con este usuario.
     */
    public function contratos(): HasMany
    {
        return $this->hasMany(Contrato::class, 'usuario', 'usuario');
    }

    /**
     * Obtiene los cobros asociados con este usuario.
     */
    public function cobros(): HasMany
    {
        return $this->hasMany(Cobro::class, 'usuario', 'usuario');
    }

    /**
     * Obtiene los cobros pendientes asociados con este usuario.
     */
    public function cobrosPendientes()
    {
        return $this->cobros()->where('pagado', 'No');
    }

    /**
     * Obtiene los cobros pagados asociados con este usuario.
     */
    public function cobrosPagados()
    {
        return $this->cobros()->where('pagado', 'Si');
    }

    /**
     * Obtiene los cobros vencidos asociados con este usuario.
     */
    public function cobrosVencidos()
    {
        return $this->cobros()->where('vencimiento', '<', now()->startOfDay())
            ->where('pagado', 'No');
    }

    /**
     * Obtiene los cobros creados por este usuario.
     */
    public function cobrosCreados(): HasMany
    {
        return $this->hasMany(Cobro::class, 'creado_por', 'usuario');
    }

    /**
     * Obtiene los cobros modificados por este usuario.
     */
    public function cobrosModificados(): HasMany
    {
        return $this->hasMany(Cobro::class, 'modificado_por', 'usuario');
    }

    /**
     * Obtiene el cliente de Conekta asociado al usuario.
     */
    public function conektaCustomer(): HasOne
    {
        return $this->hasOne(ConektaCustomer::class);
    }

    /**
     * Obtiene la suscripción activa del usuario.
     */
    public function subscription(): HasOne
    {
        return $this->hasOne(Subscription::class)
            ->whereIn('status', ['active', 'in_trial', 'past_due', 'paused']);
    }

    /**
     * Obtiene todas las suscripciones del usuario.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Determina si el usuario tiene una suscripción activa.
     *
     * @return bool
     */
    public function hasActiveSubscription(): bool
    {
        return $this->subscription && $this->subscription->isActive();
    }

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function boot(): void
    {
        parent::boot();

        // Antes de guardar, actualizar el campo pabusqueda
        static::saving(function ($user) {
            $user->updatePaBusqueda();
        });
    }

    /**
     * Actualiza el campo pabusqueda con los datos relevantes del usuario
     *
     * @return void
     */
    protected function updatePaBusqueda(): void
    {
        $fieldsToConcat = [
            'usuario',
            'logmail',
            'nombre',
            'apellidos',
            'empresa',
            'calle_numero',
            'colonia',
            'codigo_postal',
            'ciudad',
            'estado',
            'pais',
            'telefono',
            'celular',
            'email',
            'email_sec',
            'sitio_web',
            'fact_nombre',
            'fact_domicilio',
            'fact_rfc',
        ];

        $concatenatedValues = [];

        foreach ($fieldsToConcat as $field) {
            if (!empty($this->$field)) {
                $concatenatedValues[] = $this->$field;
            }
        }

        $this->pabusqueda = implode(' ', $concatenatedValues);

        // // Quito espacios dobles y hago trim
        // $this->pabusqueda = preg_replace('/\s+/', ' ', $this->pabusqueda);
        // $this->pabusqueda = trim($this->pabusqueda);
    }
}
