<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class BolsaInmobiliaria extends SistemaInmobiliarioModel
{
    use HasFactory, SoftDeletes;

    /**
     * La tabla asociada con el modelo.
     *
     * @var string
     */
    protected $table = 'bolsa_inmobiliaria';

    /**
     * Los atributos que son asignables en masa.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'contrato_solicitante',
        'contrato_solicitado',
        'conexion_aceptada',
        'bak_solicitante',
        'bak_solicitado',
        'fecha_solicitud',
        'fecha_aceptacion',
        'authorized_at',
    ];

    /**
     * Los atributos que deben convertirse a tipos nativos.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'conexion_aceptada' => 'string',
        'fecha_solicitud' => 'datetime',
        'fecha_aceptacion' => 'datetime',
        'authorized_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Estados de conexión
     */
    const CONEXION_ACEPTADA = 'Si';
    const CONEXION_PENDIENTE = 'No';

    /**
     * Obtiene el contrato solicitante (quien invita)
     */
    public function contratoSolicitante(): BelongsTo
    {
        return $this->belongsTo(SIConfig::class, 'contrato_solicitante', 'contrato');
    }

    /**
     * Obtiene el contrato solicitado (quien fue invitado)
     */
    public function contratoSolicitado(): BelongsTo
    {
        return $this->belongsTo(SIConfig::class, 'contrato_solicitado', 'contrato');
    }

    /**
     * Crear una nueva relación en la bolsa inmobiliaria
     */
    public static function crearRelacion(int $contratoSolicitante, int $contratoSolicitado): self
    {
        return self::create([
            'contrato_solicitante' => $contratoSolicitante,
            'contrato_solicitado' => $contratoSolicitado,
            'conexion_aceptada' => self::CONEXION_ACEPTADA, // Automáticamente aceptada por invitación
            'bak_solicitante' => $contratoSolicitante,
            'bak_solicitado' => $contratoSolicitado,
            'fecha_solicitud' => now(),
            'fecha_aceptacion' => now(),
            'authorized_at' => now(),
        ]);
    }

    /**
     * Verificar si ya existe una relación entre dos contratos
     */
    public static function existeRelacion(int $contratoSolicitante, int $contratoSolicitado): bool
    {
        return self::where('contrato_solicitante', $contratoSolicitante)
            ->where('contrato_solicitado', $contratoSolicitado)
            ->exists();
    }

    /**
     * Obtener todas las relaciones de un contrato
     */
    public static function relacionesDeContrato(int $contrato): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('contrato_solicitante', $contrato)
            ->orWhere('contrato_solicitado', $contrato)
            ->where('conexion_aceptada', self::CONEXION_ACEPTADA)
            ->get();
    }

    /**
     * Verificar si la conexión está aceptada
     */
    public function isAceptada(): bool
    {
        return $this->conexion_aceptada === self::CONEXION_ACEPTADA;
    }

    /**
     * Marcar la conexión como aceptada
     */
    public function aceptar(): void
    {
        $this->update([
            'conexion_aceptada' => self::CONEXION_ACEPTADA,
            'fecha_aceptacion' => now(),
            'authorized_at' => now(),
        ]);
    }
}
