<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SIConfig extends SistemaInmobiliarioModel
{
    protected $table = 'config';
    protected $primaryKey = 'contrato';
    public $incrementing = false;
    public $timestamps = true;

    protected $fillable = [
        'contrato',
        'usuario',
        'correo_ventas',
        'correo_contratacion',
        'correo_comentarios',
        'correo_dirgral',
        'dominio',
        'dominio2',
        'redes_sociales',
        'ruta',
        'inmuebles',
        'foto_ancho',
        'por_ciudades',
        'prop_por_pag',
        'prop_por_linea',
        'tipo_tema',
        'theme',
        'theme_hash',
        'theme_dev',
        'nueva_ventana',
        'ligas_comunes',
        'ultimo_acceso',
        'combinar_ampi',
        'notifica_telefono',
        'telefono_props',
        'transparencia_fotos',
        'tipo_fotos',
        'fotos_por_linea',
        'tipo_galeria',
        'socio_ampi',
        'funcion_ampi',
        'ampi_admin',
        'estado',
        'orden_inmuebles',
        'forma_orden_inmuebles',
        'enviar_nextel',
        'fotos',
        'moneda_predeterminada',
        'mostrar_monedas',
        'idioma',
        'campos_por_linea',
        'leyenda_b_favoritos',
        'lat_club_negocios',
        'ultimo_cobro',
        'dias_novedades',
        'notificar_propietarios',
        'detalles_new',
        'status',
        'servicio_contratado',
        'pagado_hasta',
        'vencido_si',
        'vencimiento',
        'prorroga',
        'saldo',
        'adeudo',
        'a_vencer',
        'saldo_a_favor',
        'credito',
        'msg_panel_desactivado',
        'presentar_con',
        'front',
        'deptID_livephp',
        'custom_data',
        'external_connections'
    ];

    /**
     * Los atributos que deben ser convertidos a tipos nativos.
     *
     * @var array
     */
    protected $casts = [
        'contrato' => 'integer',
        'inmuebles' => 'integer',
        'foto_ancho' => 'integer',
        'prop_por_pag' => 'integer',
        'prop_por_linea' => 'integer',
        'fotos_por_linea' => 'integer',
        'funcion_ampi' => 'integer',
        'campos_por_linea' => 'integer',
        'dias_novedades' => 'integer',
        'status' => 'boolean',
        'saldo' => 'float',
        'adeudo' => 'float',
        'a_vencer' => 'float',
        'saldo_a_favor' => 'float',
        'credito' => 'float',
        'front' => 'boolean',
        'deptID_livephp' => 'integer',
        'ultimo_acceso' => 'datetime',
        'ultimo_cobro' => 'date',
        'pagado_hasta' => 'date',
        'vencimiento' => 'date',
        'prorroga' => 'date',
        'msg_panel_desactivado' => 'date',
        'redes_sociales' => 'array',
        'custom_data' => 'array',
        'external_connections' => 'array'
    ];

    /**
     * Los atributos que deben ser mutados a fechas.
     *
     * @var array
     */
    protected array $dates = [
        'ultimo_acceso',
        'ultimo_cobro',
        'pagado_hasta',
        'vencimiento',
        'prorroga',
        'msg_panel_desactivado',
        'created_at',
        'updated_at'
    ];

    /**
     * Obtener si el contrato está activo.
     *
     * @return Attribute
     */
    protected function activo(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->status == 1 &&
                ($this->pagado_hasta >= now() ||
                    ($this->prorroga && $this->prorroga >= now()))
        );
    }

    /**
     * Obtener las monedas disponibles como array.
     *
     * @return Attribute
     */
    protected function monedasDisponibles(): Attribute
    {
        return Attribute::make(
            get: fn() => explode(',', $this->mostrar_monedas)
        );
    }

    /**
     * Obtiene los inmuebles asociados a esta configuración.
     *
     * @return HasMany
     */
    public function inmuebles(): HasMany
    {
        return $this->hasMany(Inmueble::class, 'contrato', 'contrato');
    }
}
